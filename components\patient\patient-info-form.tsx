"use client"

import { useState } from "react"
import { createClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { vi } from "date-fns/locale"
import { CalendarIcon, Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import type { PatientInfo } from "@/types"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"

interface PatientInfoFormProps {
  initialData?: PatientInfo
}

const formSchema = z.object({
  full_name: z.string().min(2, {
    message: "Họ tên phải có ít nhất 2 ký tự.",
  }),
  date_of_birth: z.date({
    required_error: "Vui lòng chọn ngày sinh.",
  }),
  gender: z.string({
    required_error: "Vui lòng chọn giới tính.",
  }),
  address: z.string().min(5, {
    message: "Địa chỉ phải có ít nhất 5 ký tự.",
  }),
  phone_number: z.string().min(10, {
    message: "Số điện thoại phải có ít nhất 10 ký tự.",
  }),
  email: z.string().email({
    message: "Vui lòng nhập địa chỉ email hợp lệ.",
  }),
  emergency_contact: z.string().optional(),
  blood_type: z.string().optional(),
  allergies: z.string().optional(),
  height: z.string().optional(),
  weight: z.string().optional(),
  occupation: z.string().optional(),
  insurance_info: z.string().optional(),
})

export function PatientInfoForm({ initialData }: PatientInfoFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()
  const supabase = createClient()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      full_name: initialData?.full_name || "",
      date_of_birth: initialData?.date_of_birth ? new Date(initialData.date_of_birth) : new Date(),
      gender: initialData?.gender || "",
      address: initialData?.address || "",
      phone_number: initialData?.phone_number || "",
      email: initialData?.email || "",
      emergency_contact: initialData?.emergency_contact || "",
      blood_type: initialData?.blood_type || "",
      allergies: Array.isArray(initialData?.allergies) ? initialData.allergies.join(", ") : "",
      height: initialData?.height?.toString() || "",
      weight: initialData?.weight?.toString() || "",
      occupation: initialData?.occupation || "",
      insurance_info: initialData?.insurance_info || "",
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsSubmitting(true)

      const {
        data: { user },
      } = await supabase.auth.getUser()

      if (!user) {
        toast({
          title: "Lỗi xác thực",
          description: "Vui lòng đăng nhập để cập nhật thông tin.",
          variant: "destructive",
        })
        return
      }

      const { error } = await supabase
        .from("patients")
        .upsert({
          id: user.id,
          full_name: values.full_name,
          date_of_birth: values.date_of_birth.toISOString(),
          gender: values.gender,
          address: values.address,
          phone_number: values.phone_number,
          email: values.email,
          emergency_contact: values.emergency_contact,
          blood_type: values.blood_type,
          allergies: values.allergies ? values.allergies.split(",").map((a) => a.trim()) : [],
          height: values.height,
          weight: values.weight,
          occupation: values.occupation,
          insurance_info: values.insurance_info,
        })
        .select()

      if (error) throw error

      toast({
        title: "Cập nhật thành công",
        description: "Thông tin cá nhân của bạn đã được cập nhật.",
      })
      console.log("Patient info updated successfully:", values)
      window.location.reload()
    } catch (error) {
      console.error("Error updating patient info:", error)
      toast({
        title: "Lỗi",
        description: "Đã xảy ra lỗi khi cập nhật thông tin. Vui lòng thử lại.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Họ và tên</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập họ và tên" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="date_of_birth"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Ngày sinh</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button variant={"outline"} className="w-full pl-3 text-left font-normal">
                        {field.value ? format(field.value, "PPP", { locale: vi }) : <span>Chọn ngày</span>}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="gender"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Giới tính</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn giới tính" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="male">Nam</SelectItem>
                    <SelectItem value="female">Nữ</SelectItem>
                    <SelectItem value="other">Khác</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Số điện thoại</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập số điện thoại" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập địa chỉ email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="emergency_contact"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Liên hệ khẩn cấp</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập thông tin liên hệ khẩn cấp" {...field} />
                </FormControl>
                <FormDescription>Tên và số điện thoại người liên hệ khi khẩn cấp</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="blood_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nhóm máu</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn nhóm máu" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="A+">A+</SelectItem>
                    <SelectItem value="A-">A-</SelectItem>
                    <SelectItem value="B+">B+</SelectItem>
                    <SelectItem value="B-">B-</SelectItem>
                    <SelectItem value="AB+">AB+</SelectItem>
                    <SelectItem value="AB-">AB-</SelectItem>
                    <SelectItem value="O+">O+</SelectItem>
                    <SelectItem value="O-">O-</SelectItem>
                    <SelectItem value="unknown">Không biết</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="height"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Chiều cao (cm)</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập chiều cao" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="weight"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Cân nặng (kg)</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập cân nặng" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="occupation"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nghề nghiệp</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập nghề nghiệp" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="insurance_info"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Thông tin bảo hiểm</FormLabel>
                <FormControl>
                  <Input placeholder="Nhập thông tin bảo hiểm y tế" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Địa chỉ</FormLabel>
              <FormControl>
                <Textarea placeholder="Nhập địa chỉ đầy đủ" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="allergies"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Dị ứng</FormLabel>
              <FormControl>
                <Textarea placeholder="Liệt kê các dị ứng (nếu có)" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Đang lưu...
            </>
          ) : (
            "Lưu thông tin"
          )}
        </Button>
      </form>
    </Form>
  )
}

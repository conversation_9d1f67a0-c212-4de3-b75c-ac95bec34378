"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { format } from "date-fns"
import { AlertCircle, Calendar, FileText, Pill, Stethoscope, Activity, ChevronRight } from "lucide-react"
import Link from "next/link"

interface TimelineEvent {
  id: string
  type: "medical_history" | "document" | "prescription" | "appointment" | "health_metric"
  title: string
  description: string
  date: string
  status?: string
  icon?: string
  link?: string
}

interface MedicalTimelineProps {
  patientId: string
}

export function MedicalTimeline({ patientId }: MedicalTimelineProps) {
  const [events, setEvents] = useState<TimelineEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient()

  useEffect(() => {
    async function fetchTimelineData() {
      try {
        setLoading(true)

        // Fetch medical histories
        const { data: histories, error: historiesError } = await supabase
          .from("medical_history")
          .select("id, diagnosis, date, status")
          .eq("patient_id", patientId)
          .order("created_at", { ascending: false })
          .limit(10)

        if (historiesError) throw historiesError

        // Fetch documents
        const { data: documents, error: documentsError } = await supabase
          .from("medical_documents")
          .select("id, title, description, created_at, status")
          .eq("patient_id", patientId)
          .order("created_at", { ascending: false })
          .limit(10)

        if (documentsError) throw documentsError

        // Fetch prescriptions (assuming there's a prescriptions table)
        const { data: prescriptions, error: prescriptionsError } = await supabase
          .from("prescriptions")
          .select("id, medication_name, prescribed_date, status")
          .eq("patient_id", patientId)
          .order("prescribed_date", { ascending: false })
          .limit(10)

        // It's okay if this fails, we'll just not include prescriptions

        // Fetch appointments (assuming there's an appointments table)
        const { data: appointments, error: appointmentsError } = await supabase
          .from("appointments")
          .select("id, title, appointment_date, status")
          .eq("patient_id", patientId)
          .order("appointment_date", { ascending: false })
          .limit(10)

        // It's okay if this fails, we'll just not include appointments

        // Combine all events
        const timelineEvents: TimelineEvent[] = [
          ...(histories || []).map((h) => ({
            id: h.id,
            type: "medical_history" as const,
            title: h.diagnosis,
            description: `Chẩn đoán: ${h.diagnosis}`,
            date: h.date,
            status: h.status,
            link: `/medical-records/history/${h.id}`,
          })),
          ...(documents || []).map((d) => ({
            id: d.id,
            type: "document" as const,
            title: d.title,
            description: d.description || "Tài liệu y tế",
            date: d.created_at,
            status: d.status,
            link: `/medical-records/documents/${d.id}`,
          })),
          ...(prescriptions || []).map((p) => ({
            id: p.id,
            type: "prescription" as const,
            title: p.medication_name,
            description: `Thuốc: ${p.medication_name}`,
            date: p.prescribed_date,
            status: p.status,
            link: `/prescriptions/${p.id}`,
          })),
          ...(appointments || []).map((a) => ({
            id: a.id,
            type: "appointment" as const,
            title: a.title,
            description: `Cuộc hẹn: ${a.title}`,
            date: a.appointment_date,
            status: a.status,
            link: `/appointments/${a.id}`,
          })),
        ]

        // Sort by date
        timelineEvents.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

        setEvents(timelineEvents)
      } catch (error: any) {
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    if (patientId) {
      fetchTimelineData()
    }
  }, [patientId, supabase])

  const getEventIcon = (type: string) => {
    switch (type) {
      case "medical_history":
        return <Stethoscope className="h-5 w-5 text-blue-500" />
      case "document":
        return <FileText className="h-5 w-5 text-green-500" />
      case "prescription":
        return <Pill className="h-5 w-5 text-purple-500" />
      case "appointment":
        return <Calendar className="h-5 w-5 text-orange-500" />
      case "health_metric":
        return <Activity className="h-5 w-5 text-red-500" />
      default:
        return <FileText className="h-5 w-5 text-gray-500" />
    }
  }

  const getEventStatusBadge = (type: string, status?: string) => {
    if (!status) return null

    let variant: "default" | "secondary" | "destructive" | "outline" = "outline"

    if (type === "medical_history") {
      variant = status === "active" ? "destructive" : "outline"
    } else if (type === "appointment") {
      if (status === "scheduled") variant = "default"
      else if (status === "completed") variant = "secondary"
      else if (status === "cancelled") variant = "destructive"
    } else if (type === "prescription") {
      if (status === "active") variant = "default"
      else if (status === "completed") variant = "secondary"
      else if (status === "discontinued") variant = "destructive"
    } else if (type === "document") {
      if (status === "verified") variant = "default"
      else if (status === "pending") variant = "outline"
    }

    return <Badge variant={variant}>{status}</Badge>
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center text-red-500">
            <AlertCircle className="mr-2" />
            <p>Error loading timeline: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lịch sử y tế</CardTitle>
        <CardDescription>Các sự kiện y tế gần đây</CardDescription>
      </CardHeader>
      <CardContent>
        {events.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">Chưa có dữ liệu lịch sử y tế</div>
        ) : (
          <div className="relative">
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-muted"></div>
            <div className="space-y-6">
              {events.map((event, index) => (
                <div key={`${event.type}-${event.id}`} className="relative pl-10">
                  <div className="absolute left-0 top-1 flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                    {getEventIcon(event.type)}
                  </div>

                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{event.title}</h3>
                        {getEventStatusBadge(event.type, event.status)}
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{event.description}</p>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground whitespace-nowrap">
                        {format(new Date(event.date), "dd/MM/yyyy")}
                      </span>

                      {event.link && (
                        <Button variant="ghost" size="icon" asChild>
                          <Link href={event.link}>
                            <ChevronRight className="h-4 w-4" />
                          </Link>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

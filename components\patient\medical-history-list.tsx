"use client"

import { useEffect, useState, useRef } from "react"
import { createClient } from "@/lib/supabase/client"
import type { MedicalHistory } from "@/types/patient-records"
import { Card, CardContent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { format } from "date-fns"
import { MoreVertical, Trash2, CheckCircle, AlertCircle, Calendar, Edit } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { EditHistoryDialog } from "./edit-history-dialog"
import { MedicalHistoryDetail } from "../medical-records/medical-history-detail"

interface MedicalHistoryListProps {
  patientId: string
}

export function MedicalHistoryList({ patientId }: MedicalHistoryListProps) {
  const [histories, setHistories] = useState<MedicalHistory[]>([])
  const [loading, setLoading] = useState(true)
  const [deleteHistory, setDeleteHistory] = useState<MedicalHistory | null>(null)
  const [editHistory, setEditHistory] = useState<MedicalHistory | null>(null)
  const [expandedId, setExpandedId] = useState<string | null>(null)

  const supabase = createClient()
  const { toast } = useToast()
  const expandedRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (expandedRef.current && !expandedRef.current.contains(event.target as Node)) {
        setExpandedId(null)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    const fetchHistories = async () => {
      try {
        const { data, error } = await supabase
          .from("medical_history")
          .select("*")
          .eq("patient_id", patientId)
          .order("start_date", { ascending: false })

        if (error) throw error

        setHistories(data || [])
      } catch (error) {
        console.error("Error fetching medical histories:", error)
        toast({
          title: "Lỗi",
          description: "Không thể tải danh sách tiền sử bệnh",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchHistories()

    // Thiết lập subscription để cập nhật realtime
    const channel = supabase
      .channel("medical_history_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "medical_history",
          filter: `patient_id=eq.${patientId}`,
        },
        () => {
          fetchHistories()
        },
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [patientId, supabase, toast])

  const handleDelete = async () => {
    if (!deleteHistory) return

    try {
      const { error } = await supabase.from("medical_history").delete().eq("id", deleteHistory.id)

      if (error) throw error

      // Cập nhật state
      setHistories(histories.filter((h) => h.id !== deleteHistory.id))

      toast({
        title: "Đã xóa",
        description: "Thông tin tiền sử bệnh đã được xóa thành công",
      })
    } catch (error) {
      console.error("Error deleting medical history:", error)
      toast({
        title: "Lỗi khi xóa",
        description: "Đã xảy ra lỗi khi xóa thông tin",
        variant: "destructive",
      })
    } finally {
      setDeleteHistory(null)
    }
  }

  const getHistoryTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      diagnosis: "Chẩn đoán",
      surgery: "Phẫu thuật",
      treatment: "Điều trị",
      medication: "Thuốc",
      allergy: "Dị ứng",
      family_history: "Tiền sử gia đình",
      other: "Khác",
    }

    return types[type] || "Không xác định"
  }

  const getHistoryTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      diagnosis: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      surgery: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      treatment: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      medication: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
      allergy: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      family_history: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300",
      other: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
    }

    return colors[type] || colors.other
  }

  const handleUpdated = (updated: MedicalHistory) => {
    setHistories((prev) =>
      prev.map((h) => (h.id === updated.id ? { ...h, ...updated } : h)),
    )
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (histories.length === 0) {
    return (
      <div className="text-center py-8">
        <Calendar className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-lg font-medium">Chưa có thông tin tiền sử bệnh</h3>
        <p className="mt-1 text-sm text-gray-500">
          Thêm thông tin tiền sử bệnh để bác sĩ có thể hiểu rõ hơn về tình trạng sức khỏe của bạn.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {histories.map((history) => (
        <Card key={history.id} className="cursor-pointer" ref={expandedId === history.id ? expandedRef : null} onClick={() => setExpandedId(history.id)} >
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Badge className={getHistoryTypeColor(history.history_type)}>
                    {getHistoryTypeLabel(history.history_type)}
                  </Badge>
                  {history.is_current && (
                    <Badge variant="outline" className="border-amber-500 text-amber-500">
                      Đang tiếp diễn
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-lg">{history.title}</CardTitle>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setEditHistory(history)}>
                    <Edit className="mr-2 h-4 w-4" />
                    <span>Chỉnh sửa</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setDeleteHistory(history)}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    <span>Xóa</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent className="pb-2">
            {history.description && <p className="text-sm text-gray-500">{history.description}</p>}
          </CardContent>
          <CardFooter className="flex justify-between pt-0">
            <div className="flex items-center text-xs text-gray-500">
              <span>
                {history.start_date && format(new Date(history.start_date), "dd/MM/yyyy")}
                {" - "}
                {history.is_current
                  ? "Hiện tại"
                  : history.end_date
                    ? format(new Date(history.end_date), "dd/MM/yyyy")
                    : "Không xác định"}
              </span>
            </div>
            <div>
              {history.is_verified ? (
                <div className="flex items-center text-green-600 text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  <span>Đã xác thực</span>
                </div>
              ) : (
                <div className="flex items-center text-amber-600 text-xs">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  <span>Chưa xác thực</span>
                </div>
              )}
            </div>
          </CardFooter>

          {expandedId === history.id && (
            <CardContent className="pt-2">
              <MedicalHistoryDetail historyId={history.id} />
            </CardContent>
          )} 
        </Card>
      ))}

      {/* Dialog xác nhận xóa */}
      <AlertDialog open={!!deleteHistory} onOpenChange={(open) => !open && setDeleteHistory(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa thông tin "{deleteHistory?.title}"? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog chỉnh sửa tiền sử bệnh */}
      <EditHistoryDialog
        open={!!editHistory}
        onOpenChange={(open) => {
          if (!open) setEditHistory(null)
        }}
        history={editHistory}
        onUpdated={handleUpdated}
      />
    </div>
  )
}

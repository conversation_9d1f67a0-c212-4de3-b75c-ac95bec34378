"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2, CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { vi } from "date-fns/locale"
import { useToast } from "@/components/ui/use-toast"
import { createClient } from "@/lib/supabase/client"
import type { MedicalHistory } from "@/types/patient-records"

interface EditHistoryDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    history: MedicalHistory | null
    onUpdated: (updatedHistory: MedicalHistory) => void
}

export function EditHistoryDialog({ open, onOpenChange, history, onUpdated }: EditHistoryDialogProps) {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [historyType, setHistoryType] = useState("")
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  const [isCurrent, setIsCurrent] = useState(false)
  const [loading, setLoading] = useState(false)

  const { toast } = useToast()
  const supabase = createClient()

  // Khởi tạo lại dữ liệu khi mở dialog
  useEffect(() => {
    if (history) {
      setTitle(history.title || "")
      setDescription(history.description || "")
      setHistoryType(history.history_type || "")
      setStartDate(history.start_date ? new Date(history.start_date) : undefined)
      setEndDate(history.end_date ? new Date(history.end_date) : undefined)
      setIsCurrent(history.is_current ?? false)
    }
  }, [history])

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!history) return

    if (!title || !historyType || !startDate) {
      toast({
        title: "Thiếu thông tin",
        description: "Vui lòng điền đầy đủ thông tin bắt buộc",
        variant: "destructive",
      })
      return
    }

    setLoading(true)

    try {
      const { error } = await supabase
        .from("medical_history")
        .update({
          title,
          description,
          history_type: historyType,
          start_date: startDate.toISOString(),
          end_date: !isCurrent ? endDate?.toISOString() : null,
          is_current: isCurrent,
        })
        .eq("id", history.id)

      if (error) throw error

      toast({
        title: "Cập nhật thành công",
        description: "Tiền sử bệnh đã được cập nhật",
      })

      onUpdated({
        ...history,
        title,
        description,
        history_type: historyType as MedicalHistory["history_type"],
        start_date: startDate.toISOString(),
        end_date: endDate?.toISOString(),
        is_current: isCurrent,
      })
      
      onOpenChange(false) // đóng dialog
    } catch (error) {
      console.error("Lỗi khi cập nhật:", error)
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật thông tin",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (!history) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange} >
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Chỉnh sửa tiền sử bệnh</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleUpdate} className="space-y-4">
          <div className="space-y-2">
            <Label>Tiêu đề</Label>
            <Input value={title} onChange={(e) => setTitle(e.target.value)} required />
          </div>

          <div className="space-y-2">
            <Label>Loại</Label>
            <Select value={historyType} onValueChange={setHistoryType}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn loại" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="diagnosis">Chẩn đoán</SelectItem>
                <SelectItem value="surgery">Phẫu thuật</SelectItem>
                <SelectItem value="treatment">Điều trị</SelectItem>
                <SelectItem value="medication">Thuốc</SelectItem>
                <SelectItem value="allergy">Dị ứng</SelectItem>
                <SelectItem value="family_history">Tiền sử gia đình</SelectItem>
                <SelectItem value="other">Khác</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Mô tả</Label>
            <Textarea value={description} onChange={(e) => setDescription(e.target.value)} rows={3} />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Ngày bắt đầu</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP", { locale: vi }) : "Chọn ngày"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>Ngày kết thúc</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal" disabled={isCurrent}>
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP", { locale: vi }) : "Chọn ngày"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    disabled={(date) => (startDate ? date < startDate : false)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is-current"
              checked={isCurrent}
              onCheckedChange={(checked) => {
                setIsCurrent(checked === true)
                if (checked) setEndDate(undefined)
              }}
            />
            <Label htmlFor="is-current">Đang tiếp diễn</Label>
          </div>

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Đang lưu...
              </>
            ) : (
              "Lưu thay đổi"
            )}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  )
}

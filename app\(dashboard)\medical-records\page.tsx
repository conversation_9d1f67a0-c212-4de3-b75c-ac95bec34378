import { createClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { DocumentUpload } from "@/components/patient/document-upload"
import { MedicalHistoryForm } from "@/components/patient/medical-history-form"
import { PatientInfoForm } from "@/components/patient/patient-info-form"
import { MedicalRecordSharing } from "@/components/patient/medical-record-sharing"
import { Button } from "@/components/ui/button"
import { FileText, History, User, Share2, BarChart, Clock, Activity } from "lucide-react"
import Link from "next/link"

// Import các component mới
import { HealthOverview } from "@/components/medical-records/health-overview"
import { MedicalTimeline } from "@/components/medical-records/medical-timeline"
import { HealthMetricsChart } from "@/components/medical-records/health-metrics-chart"
import { DocumentGallery } from "@/components/medical-records/document-gallery"
import { MedicalHistoryDetail } from "@/components/medical-records/medical-history-detail"
import { MedicalRecordSummary } from "@/components/medical-records/medical-record-summary"
import { MedicalHistoryList } from "@/components/patient/medical-history-list"

export default async function MedicalRecordsPage() {
  const supabase = createClient()

  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect("/login")
  }

  // Kiểm tra xem người dùng có phải là bệnh nhân không
  const { data: userData } = await supabase.from("users").select("role").eq("id", session.user.id).single()

  if (userData?.role !== "patient") {
    redirect("/dashboard")
  }

  // Lấy thông tin bệnh nhân
  const { data: patientData } = await supabase.from("patients").select("*").eq("id", session.user.id).single()

  // Lấy số lượng tài liệu
  const { count: documentCount } = await supabase
    .from("patient_documents")
    .select("*", { count: "exact", head: true })
    .eq("patient_id", session.user.id)

  // Lấy số lượng tiền sử bệnh
  const { count: historyCount } = await supabase
    .from("medical_history")
    .select("*", { count: "exact", head: true })
    .eq("patient_id", session.user.id)

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Hồ sơ y tế của tôi</h1>
        <p className="text-muted-foreground">
          Quản lý thông tin y tế cá nhân, tài liệu và tiền sử bệnh để được chăm sóc tốt hơn
        </p>
      </div>

      {/* Thêm component tổng quan hồ sơ y tế */}
      <MedicalRecordSummary 
        patientId={session.user.id} 
        patientName={patientData?.full_name} 
        patientInfo={{
          age: patientData?.date_of_birth
            ? new Date().getFullYear() - new Date(patientData.date_of_birth).getFullYear()
            : 1,
          gender:
            patientData?.gender === "male"
              ? "Nam"
              : patientData?.gender === "female"
              ? "Nữ"
              : "Khác",
          bloodType: patientData?.blood_type,
          allergies: patientData?.allergies,
        }}
        stats={{
          documentsCount: documentCount,
          conditionsCount: historyCount,
          medicationsCount: 0,
          appointmentsCount: 0,
        }}
        upcomingAppointment={{
          id: "appt-1",
          date: "2025-06-20T09:30:00",
          doctor: "BS. Lê Văn C",
          purpose: "Khám theo dõi tình trạng tim mạch",
        }}
      />

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid grid-cols-2 md:grid-cols-7 mb-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            <span className="hidden md:inline">Tổng quan</span>
            <span className="md:hidden">Tổng quan</span>
          </TabsTrigger>
          <TabsTrigger value="timeline" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span className="hidden md:inline">Dòng thời gian</span>
            <span className="md:hidden">Timeline</span>
          </TabsTrigger>
          <TabsTrigger value="info" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span className="hidden md:inline">Thông tin cá nhân</span>
            <span className="md:hidden">Thông tin</span>
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="hidden md:inline">Tài liệu y tế</span>
            <span className="md:hidden">Tài liệu</span>
            {documentCount ? <span className="ml-1 text-xs">({documentCount})</span> : null}
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            <span className="hidden md:inline">Tiền sử bệnh</span>
            <span className="md:hidden">Tiền sử</span>
            {historyCount ? <span className="ml-1 text-xs">({historyCount})</span> : null}
          </TabsTrigger>
          <TabsTrigger value="metrics" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            <span className="hidden md:inline">Chỉ số sức khỏe</span>
            <span className="md:hidden">Chỉ số</span>
          </TabsTrigger>
          <TabsTrigger value="sharing" className="flex items-center gap-2">
            <Share2 className="h-4 w-4" />
            <span className="hidden md:inline">Chia sẻ hồ sơ</span>
            <span className="md:hidden">Chia sẻ</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="space-y-6">
            <HealthOverview patientId={session.user.id} />
          </div>
        </TabsContent>

        <TabsContent value="timeline">
          <div className="space-y-6">
            <MedicalTimeline patientId={session.user.id} />
          </div>
        </TabsContent>

        <TabsContent value="info">
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Thông tin cá nhân</h2>
              <PatientInfoForm initialData={patientData} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="documents">
          <div className="space-y-6">
            <DocumentGallery patientId={session.user.id} />

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6" id="upload-document">
              <h2 className="text-xl font-semibold mb-4">Tải lên tài liệu y tế</h2>
              <DocumentUpload patientId={session.user.id} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="history">
          <div className="space-y-6">
            <MedicalHistoryList patientId={session.user.id} />

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6" id="add-history">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Thêm tiền sử bệnh</h2>
              </div>
              <MedicalHistoryForm patientId={session.user.id} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="metrics">
          <div className="space-y-6">
            <HealthMetricsChart patientId={session.user.id} />

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Quản lý chỉ số sức khỏe</h2>
                <Button asChild>
                  <Link href="/health-metrics">
                    <BarChart className="h-4 w-4 mr-2" />
                    Xem chi tiết chỉ số
                  </Link>
                </Button>
              </div>
              <p className="text-muted-foreground mb-4">
                Theo dõi các chỉ số sức khỏe quan trọng như cân nặng, huyết áp, đường huyết và nhiều chỉ số khác.
              </p>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800">
                  <h3 className="font-medium text-blue-800 dark:text-blue-300">Cân nặng</h3>
                  <p className="text-sm text-blue-600 dark:text-blue-400">
                    Theo dõi cân nặng theo thời gian để đánh giá hiệu quả điều trị
                  </p>
                </div>
                <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-100 dark:border-red-800">
                  <h3 className="font-medium text-red-800 dark:text-red-300">Huyết áp</h3>
                  <p className="text-sm text-red-600 dark:text-red-400">
                    Ghi lại huyết áp để theo dõi sức khỏe tim mạch
                  </p>
                </div>
                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-100 dark:border-purple-800">
                  <h3 className="font-medium text-purple-800 dark:text-purple-300">Đường huyết</h3>
                  <p className="text-sm text-purple-600 dark:text-purple-400">
                    Theo dõi đường huyết để kiểm soát tình trạng đường trong máu
                  </p>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="sharing">
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Chia sẻ hồ sơ y tế</h2>
              <MedicalRecordSharing patientId={session.user.id} />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

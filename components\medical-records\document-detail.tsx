"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Calendar, Download, FileText, User, AlertCircle, Tag, Eye, Share2 } from "lucide-react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { format } from "date-fns"

interface Document {
  id: string
  title: string
  description: string
  file_path: string
  file_type: string
  file_size: number
  created_at: string
  patient_id: string
  uploaded_by: string
  category: string
  tags: string[]
  status: string
  patients?: {
    id: string
    full_name: string
  }
  users?: {
    id: string
    full_name: string
  }
}

interface DocumentDetailProps {
  documentId: string
}

export function DocumentDetail({ documentId }: DocumentDetailProps) {
  const [document, setDocument] = useState<Document | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient()

  useEffect(() => {
    async function fetchDocument() {
      try {
        setLoading(true)
        const { data, error } = await supabase
          .from("medical_documents")
          .select(`
            *,
            patients!inner(id, full_name),
            users:uploaded_by(id, full_name)
          `)
          .eq("id", documentId)
          .single()

        if (error) throw error
        setDocument(data)
      } catch (error: any) {
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    if (documentId) {
      fetchDocument()
    }
  }, [documentId, supabase])

  const handleDownload = async () => {
    if (!document) return

    try {
      const { data, error } = await supabase.storage.from("medical_documents").download(document.file_path)

      if (error) throw error

      // Create a download link and trigger download
      const url = URL.createObjectURL(data)
      const a = document.createElement("a")
      a.href = url
      a.download = document.title + "." + document.file_type
      document.body.appendChild(a)
      a.click()
      URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error: any) {
      console.error("Error downloading file:", error.message)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex items-center text-red-500">
            <AlertCircle className="mr-2" />
            <p>Error loading document: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!document) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <p className="text-muted-foreground">No document found</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-bold">{document.title}</CardTitle>
            <CardDescription>
              <div className="flex items-center mt-1">
                <Calendar className="h-4 w-4 mr-1" />
                <span className="text-sm">
                  {document.created_at ? format(new Date(document.created_at), "PPP") : "Date not available"}
                </span>
              </div>
            </CardDescription>
          </div>
          <Badge variant={document.status === "verified" ? "default" : "outline"}>{document.status}</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium text-muted-foreground mb-2">Thông tin tài liệu</h4>
            <div className="bg-muted p-3 rounded-md space-y-2">
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2 text-primary" />
                <span>Loại: {document.file_type.toUpperCase()}</span>
              </div>
              <div className="flex items-center">
                <Tag className="h-4 w-4 mr-2 text-primary" />
                <span>Kích thước: {formatFileSize(document.file_size)}</span>
              </div>
              <div className="flex items-center">
                <Tag className="h-4 w-4 mr-2 text-primary" />
                <span>Danh mục: {document.category}</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-muted-foreground mb-2">Thông tin người tải lên</h4>
            <div className="bg-muted p-3 rounded-md space-y-2">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2 text-primary" />
                <span>{document.users?.full_name || "Unknown"}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-primary" />
                <span>{document.created_at ? format(new Date(document.created_at), "PPP") : "N/A"}</span>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        <div>
          <h4 className="text-sm font-medium text-muted-foreground mb-2">Mô tả</h4>
          <p className="bg-muted p-3 rounded-md">{document.description || "Không có mô tả"}</p>
        </div>

        {document.tags && document.tags.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-muted-foreground mb-2">Tags</h4>
            <div className="flex flex-wrap gap-2">
              {document.tags.map((tag, index) => (
                <Badge key={index} variant="secondary">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        <div className="bg-muted p-4 rounded-md flex items-center justify-between">
          <div className="flex items-center">
            <FileText className="h-5 w-5 mr-2 text-primary" />
            <span className="font-medium">
              {document.title}.{document.file_type}
            </span>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              Tải xuống
            </Button>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              Xem
            </Button>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <div className="flex items-center text-sm text-muted-foreground">
          <User className="h-4 w-4 mr-1" />
          <span>Bệnh nhân: {document.patients?.full_name}</span>
        </div>
        <Button variant="outline" size="sm">
          <Share2 className="h-4 w-4 mr-2" />
          Chia sẻ
        </Button>
      </CardFooter>
    </Card>
  )
}

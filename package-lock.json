{
    "name": "my-v0-project",
    "version": "0.1.0",
    "lockfileVersion": 3,
    "requires": true,
    "packages": {
        "": {
            "name": "my-v0-project",
            "version": "0.1.0",
            "dependencies": {
                "@emotion/is-prop-valid": "latest",
                "@hookform/resolvers": "latest",
                "@radix-ui/react-accordion": "1.2.2",
                "@radix-ui/react-alert-dialog": "1.1.4",
                "@radix-ui/react-aspect-ratio": "1.1.1",
                "@radix-ui/react-avatar": "1.1.2",
                "@radix-ui/react-checkbox": "1.1.3",
                "@radix-ui/react-collapsible": "1.1.2",
                "@radix-ui/react-context-menu": "2.2.4",
                "@radix-ui/react-dialog": "1.1.4",
                "@radix-ui/react-dropdown-menu": "2.1.4",
                "@radix-ui/react-hover-card": "1.1.4",
                "@radix-ui/react-label": "2.1.1",
                "@radix-ui/react-menubar": "1.1.4",
                "@radix-ui/react-navigation-menu": "1.2.3",
                "@radix-ui/react-popover": "1.1.4",
                "@radix-ui/react-progress": "1.1.1",
                "@radix-ui/react-radio-group": "1.2.2",
                "@radix-ui/react-scroll-area": "1.2.2",
                "@radix-ui/react-select": "2.1.4",
                "@radix-ui/react-separator": "1.1.1",
                "@radix-ui/react-slider": "1.2.2",
                "@radix-ui/react-slot": "1.1.1",
                "@radix-ui/react-switch": "1.1.2",
                "@radix-ui/react-tabs": "1.1.2",
                "@radix-ui/react-toast": "1.2.4",
                "@radix-ui/react-toggle": "1.1.1",
                "@radix-ui/react-toggle-group": "1.1.1",
                "@radix-ui/react-tooltip": "1.1.6",
                "@supabase/auth-helpers-nextjs": "latest",
                "@supabase/ssr": "^0.6.1",
                "@supabase/supabase-js": "latest",
                "autoprefixer": "^10.4.20",
                "chart.js": "latest",
                "class-variance-authority": "^0.7.1",
                "clsx": "^2.1.1",
                "cmdk": "latest",
                "date-fns": "latest",
                "embla-carousel-react": "8.5.1",
                "framer-motion": "latest",
                "input-otp": "1.4.1",
                "lucide-react": "^0.454.0",
                "nanoid": "latest",
                "next": "15.2.4",
                "next-themes": "latest",
                "react": "^19",
                "react-chartjs-2": "latest",
                "react-day-picker": "8.10.1",
                "react-dom": "^19",
                "react": "^18.3.1",
                "react-chartjs-2": "latest",
                "react-day-picker": "8.10.1",
                "react-dom": "^18.3.1",
                "react-hook-form": "latest",
                "react-resizable-panels": "^2.1.7",
                "recharts": "latest",
                "sonner": "^1.7.1",
                "tailwind-merge": "^2.5.5",
                "tailwindcss-animate": "^1.0.7",
                "vaul": "^0.9.6",
                "zod": "latest"
            },
            "devDependencies": {
                "@types/node": "^22",
                "@types/node": "^22.15.32",
                "@types/react": "^19",
                "@types/react-dom": "^19",
                "postcss": "^8",
                "tailwindcss": "^3.4.17",
                "typescript": "^5"
            }
        },
        "node_modules/@alloc/quick-lru": {
            "version": "5.2.0",
            "resolved": "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz",
            "integrity": "sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=10"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/@babel/runtime": {
            "version": "7.27.6",
            "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz",
            "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==",
            "license": "MIT",
            "engines": {
                "node": ">=6.9.0"
            }
        },
        "node_modules/@emnapi/runtime": {
            "version": "1.4.3",
            "resolved": "https://registry.npmjs.org/@emnapi/runtime/-/runtime-1.4.3.tgz",
            "integrity": "sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==",
            "license": "MIT",
            "optional": true,
            "dependencies": {
                "tslib": "^2.4.0"
            }
        },
        "node_modules/@emotion/is-prop-valid": {
            "version": "1.3.1",
            "resolved": "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-1.3.1.tgz",
            "integrity": "sha512-/ACwoqx7XQi9knQs/G0qKvv5teDMhD7bXYns9N/wM8ah8iNb8jZ2uNO0YOgiq2o2poIvVtJS2YALasQuMSQ7Kw==",
            "license": "MIT",
            "dependencies": {
                "@emotion/memoize": "^0.9.0"
            }
        },
        "node_modules/@emotion/memoize": {
            "version": "0.9.0",
            "resolved": "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.9.0.tgz",
            "integrity": "sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==",
            "license": "MIT"
        },
        "node_modules/@floating-ui/core": {
            "version": "1.7.1",
            "resolved": "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.1.tgz",
            "integrity": "sha512-azI0DrjMMfIug/ExbBaeDVJXcY0a7EPvPjb2xAJPa4HeimBX+Z18HK8QQR3jb6356SnDDdxx+hinMLcJEDdOjw==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/utils": "^0.2.9"
            }
        },
        "node_modules/@floating-ui/dom": {
            "version": "1.7.1",
            "resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.1.tgz",
            "integrity": "sha512-cwsmW/zyw5ltYTUeeYJ60CnQuPqmGwuGVhG9w0PRaRKkAyi38BT5CKrpIbb+jtahSwUl04cWzSx9ZOIxeS6RsQ==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/core": "^1.7.1",
                "@floating-ui/utils": "^0.2.9"
            }
        },
        "node_modules/@floating-ui/react-dom": {
            "version": "2.1.3",
            "resolved": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.3.tgz",
            "integrity": "sha512-huMBfiU9UnQ2oBwIhgzyIiSpVgvlDstU8CX0AF+wS+KzmYMs0J2a3GwuFHV1Lz+jlrQGeC1fF+Nv0QoumyV0bA==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/dom": "^1.0.0"
            },
            "peerDependencies": {
                "react": ">=16.8.0",
                "react-dom": ">=16.8.0"
            }
        },
        "node_modules/@floating-ui/utils": {
            "version": "0.2.9",
            "resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.9.tgz",
            "integrity": "sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==",
            "license": "MIT"
        },
        "node_modules/@hookform/resolvers": {
            "version": "5.1.1",
            "resolved": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-5.1.1.tgz",
            "integrity": "sha512-J/NVING3LMAEvexJkyTLjruSm7aOFx7QX21pzkiJfMoNG0wl5aFEjLTl7ay7IQb9EWY6AkrBy7tHL2Alijpdcg==",
            "license": "MIT",
            "dependencies": {
                "@standard-schema/utils": "^0.3.0"
            },
            "peerDependencies": {
                "react-hook-form": "^7.55.0"
            }
        },
        "node_modules/@img/sharp-darwin-arm64": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.5.tgz",
            "integrity": "sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==",
            "cpu": [
                "arm64"
            ],
            "license": "Apache-2.0",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            },
            "optionalDependencies": {
                "@img/sharp-libvips-darwin-arm64": "1.0.4"
            }
        },
        "node_modules/@img/sharp-darwin-x64": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-darwin-x64/-/sharp-darwin-x64-0.33.5.tgz",
            "integrity": "sha512-fyHac4jIc1ANYGRDxtiqelIbdWkIuQaI84Mv45KvGRRxSAa7o7d1ZKAOBaYbnepLC1WqxfpimdeWfvqqSGwR2Q==",
            "cpu": [
                "x64"
            ],
            "license": "Apache-2.0",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            },
            "optionalDependencies": {
                "@img/sharp-libvips-darwin-x64": "1.0.4"
            }
        },
        "node_modules/@img/sharp-libvips-darwin-arm64": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/@img/sharp-libvips-darwin-arm64/-/sharp-libvips-darwin-arm64-1.0.4.tgz",
            "integrity": "sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==",
            "cpu": [
                "arm64"
            ],
            "license": "LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "darwin"
            ],
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-libvips-darwin-x64": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/@img/sharp-libvips-darwin-x64/-/sharp-libvips-darwin-x64-1.0.4.tgz",
            "integrity": "sha512-xnGR8YuZYfJGmWPvmlunFaWJsb9T/AO2ykoP3Fz/0X5XV2aoYBPkX6xqCQvUTKKiLddarLaxpzNe+b1hjeWHAQ==",
            "cpu": [
                "x64"
            ],
            "license": "LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "darwin"
            ],
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-libvips-linux-arm": {
            "version": "1.0.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-arm/-/sharp-libvips-linux-arm-1.0.5.tgz",
            "integrity": "sha512-gvcC4ACAOPRNATg/ov8/MnbxFDJqf/pDePbBnuBDcjsI8PssmjoKMAz4LtLaVi+OnSb5FK/yIOamqDwGmXW32g==",
            "cpu": [
                "arm"
            ],
            "license": "LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "linux"
            ],
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-libvips-linux-arm64": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-arm64/-/sharp-libvips-linux-arm64-1.0.4.tgz",
            "integrity": "sha512-9B+taZ8DlyyqzZQnoeIvDVR/2F4EbMepXMc/NdVbkzsJbzkUjhXv/70GQJ7tdLA4YJgNP25zukcxpX2/SueNrA==",
            "cpu": [
                "arm64"
            ],
            "license": "LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "linux"
            ],
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-libvips-linux-s390x": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-s390x/-/sharp-libvips-linux-s390x-1.0.4.tgz",
            "integrity": "sha512-u7Wz6ntiSSgGSGcjZ55im6uvTrOxSIS8/dgoVMoiGE9I6JAfU50yH5BoDlYA1tcuGS7g/QNtetJnxA6QEsCVTA==",
            "cpu": [
                "s390x"
            ],
            "license": "LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "linux"
            ],
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-libvips-linux-x64": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linux-x64/-/sharp-libvips-linux-x64-1.0.4.tgz",
            "integrity": "sha512-MmWmQ3iPFZr0Iev+BAgVMb3ZyC4KeFc3jFxnNbEPas60e1cIfevbtuyf9nDGIzOaW9PdnDciJm+wFFaTlj5xYw==",
            "cpu": [
                "x64"
            ],
            "license": "LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "linux"
            ],
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-libvips-linuxmusl-arm64": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linuxmusl-arm64/-/sharp-libvips-linuxmusl-arm64-1.0.4.tgz",
            "integrity": "sha512-9Ti+BbTYDcsbp4wfYib8Ctm1ilkugkA/uscUn6UXK1ldpC1JjiXbLfFZtRlBhjPZ5o1NCLiDbg8fhUPKStHoTA==",
            "cpu": [
                "arm64"
            ],
            "license": "LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "linux"
            ],
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-libvips-linuxmusl-x64": {
            "version": "1.0.4",
            "resolved": "https://registry.npmjs.org/@img/sharp-libvips-linuxmusl-x64/-/sharp-libvips-linuxmusl-x64-1.0.4.tgz",
            "integrity": "sha512-viYN1KX9m+/hGkJtvYYp+CCLgnJXwiQB39damAO7WMdKWlIhmYTfHjwSbQeUK/20vY154mwezd9HflVFM1wVSw==",
            "cpu": [
                "x64"
            ],
            "license": "LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "linux"
            ],
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-linux-arm": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-linux-arm/-/sharp-linux-arm-0.33.5.tgz",
            "integrity": "sha512-JTS1eldqZbJxjvKaAkxhZmBqPRGmxgu+qFKSInv8moZ2AmT5Yib3EQ1c6gp493HvrvV8QgdOXdyaIBrhvFhBMQ==",
            "cpu": [
                "arm"
            ],
            "license": "Apache-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            },
            "optionalDependencies": {
                "@img/sharp-libvips-linux-arm": "1.0.5"
            }
        },
        "node_modules/@img/sharp-linux-arm64": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-linux-arm64/-/sharp-linux-arm64-0.33.5.tgz",
            "integrity": "sha512-JMVv+AMRyGOHtO1RFBiJy/MBsgz0x4AWrT6QoEVVTyh1E39TrCUpTRI7mx9VksGX4awWASxqCYLCV4wBZHAYxA==",
            "cpu": [
                "arm64"
            ],
            "license": "Apache-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            },
            "optionalDependencies": {
                "@img/sharp-libvips-linux-arm64": "1.0.4"
            }
        },
        "node_modules/@img/sharp-linux-s390x": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-linux-s390x/-/sharp-linux-s390x-0.33.5.tgz",
            "integrity": "sha512-y/5PCd+mP4CA/sPDKl2961b+C9d+vPAveS33s6Z3zfASk2j5upL6fXVPZi7ztePZ5CuH+1kW8JtvxgbuXHRa4Q==",
            "cpu": [
                "s390x"
            ],
            "license": "Apache-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            },
            "optionalDependencies": {
                "@img/sharp-libvips-linux-s390x": "1.0.4"
            }
        },
        "node_modules/@img/sharp-linux-x64": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-linux-x64/-/sharp-linux-x64-0.33.5.tgz",
            "integrity": "sha512-opC+Ok5pRNAzuvq1AG0ar+1owsu842/Ab+4qvU879ippJBHvyY5n2mxF1izXqkPYlGuP/M556uh53jRLJmzTWA==",
            "cpu": [
                "x64"
            ],
            "license": "Apache-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            },
            "optionalDependencies": {
                "@img/sharp-libvips-linux-x64": "1.0.4"
            }
        },
        "node_modules/@img/sharp-linuxmusl-arm64": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-linuxmusl-arm64/-/sharp-linuxmusl-arm64-0.33.5.tgz",
            "integrity": "sha512-XrHMZwGQGvJg2V/oRSUfSAfjfPxO+4DkiRh6p2AFjLQztWUuY/o8Mq0eMQVIY7HJ1CDQUJlxGGZRw1a5bqmd1g==",
            "cpu": [
                "arm64"
            ],
            "license": "Apache-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            },
            "optionalDependencies": {
                "@img/sharp-libvips-linuxmusl-arm64": "1.0.4"
            }
        },
        "node_modules/@img/sharp-linuxmusl-x64": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-linuxmusl-x64/-/sharp-linuxmusl-x64-0.33.5.tgz",
            "integrity": "sha512-WT+d/cgqKkkKySYmqoZ8y3pxx7lx9vVejxW/W4DOFMYVSkErR+w7mf2u8m/y4+xHe7yY9DAXQMWQhpnMuFfScw==",
            "cpu": [
                "x64"
            ],
            "license": "Apache-2.0",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            },
            "optionalDependencies": {
                "@img/sharp-libvips-linuxmusl-x64": "1.0.4"
            }
        },
        "node_modules/@img/sharp-wasm32": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-wasm32/-/sharp-wasm32-0.33.5.tgz",
            "integrity": "sha512-ykUW4LVGaMcU9lu9thv85CbRMAwfeadCJHRsg2GmeRa/cJxsVY9Rbd57JcMxBkKHag5U/x7TSBpScF4U8ElVzg==",
            "cpu": [
                "wasm32"
            ],
            "license": "Apache-2.0 AND LGPL-3.0-or-later AND MIT",
            "optional": true,
            "dependencies": {
                "@emnapi/runtime": "^1.2.0"
            },
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-win32-ia32": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-win32-ia32/-/sharp-win32-ia32-0.33.5.tgz",
            "integrity": "sha512-T36PblLaTwuVJ/zw/LaH0PdZkRz5rd3SmMHX8GSmR7vtNSP5Z6bQkExdSK7xGWyxLw4sUknBuugTelgw2faBbQ==",
            "cpu": [
                "ia32"
            ],
            "license": "Apache-2.0 AND LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@img/sharp-win32-x64": {
            "version": "0.33.5",
            "resolved": "https://registry.npmjs.org/@img/sharp-win32-x64/-/sharp-win32-x64-0.33.5.tgz",
            "integrity": "sha512-MpY/o8/8kj+EcnxwvrP4aTJSWw/aZ7JIGR4aBeZkZw5B7/Jn+tY9/VNwtcoGmdT7GfggGIU4kygOMSbYnOrAbg==",
            "cpu": [
                "x64"
            ],
            "license": "Apache-2.0 AND LGPL-3.0-or-later",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
            },
            "funding": {
                "url": "https://opencollective.com/libvips"
            }
        },
        "node_modules/@isaacs/cliui": {
            "version": "8.0.2",
            "resolved": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz",
            "integrity": "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "string-width": "^5.1.2",
                "string-width-cjs": "npm:string-width@^4.2.0",
                "strip-ansi": "^7.0.1",
                "strip-ansi-cjs": "npm:strip-ansi@^6.0.1",
                "wrap-ansi": "^8.1.0",
                "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"
            },
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/@jridgewell/gen-mapping": {
            "version": "0.3.8",
            "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz",
            "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@jridgewell/set-array": "^1.2.1",
                "@jridgewell/sourcemap-codec": "^1.4.10",
                "@jridgewell/trace-mapping": "^0.3.24"
            },
            "engines": {
                "node": ">=6.0.0"
            }
        },
        "node_modules/@jridgewell/resolve-uri": {
            "version": "3.1.2",
            "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz",
            "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=6.0.0"
            }
        },
        "node_modules/@jridgewell/set-array": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz",
            "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=6.0.0"
            }
        },
        "node_modules/@jridgewell/sourcemap-codec": {
            "version": "1.5.0",
            "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz",
            "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/@jridgewell/trace-mapping": {
            "version": "0.3.25",
            "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz",
            "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@jridgewell/resolve-uri": "^3.1.0",
                "@jridgewell/sourcemap-codec": "^1.4.14"
            }
        },
        "node_modules/@kurkle/color": {
            "version": "0.3.4",
            "resolved": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.4.tgz",
            "integrity": "sha512-M5UknZPHRu3DEDWoipU6sE8PdkZ6Z/S+v4dD+Ke8IaNlpdSQah50lz1KtcFBa2vsdOnwbbnxJwVM4wty6udA5w==",
            "license": "MIT"
        },
        "node_modules/@next/env": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/@next/env/-/env-15.2.4.tgz",
            "integrity": "sha512-+SFtMgoiYP3WoSswuNmxJOCwi06TdWE733D+WPjpXIe4LXGULwEaofiiAy6kbS0+XjM5xF5n3lKuBwN2SnqD9g==",
            "license": "MIT"
        },
        "node_modules/@next/swc-darwin-arm64": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/@next/swc-darwin-arm64/-/swc-darwin-arm64-15.2.4.tgz",
            "integrity": "sha512-1AnMfs655ipJEDC/FHkSr0r3lXBgpqKo4K1kiwfUf3iE68rDFXZ1TtHdMvf7D0hMItgDZ7Vuq3JgNMbt/+3bYw==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@next/swc-darwin-x64": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/@next/swc-darwin-x64/-/swc-darwin-x64-15.2.4.tgz",
            "integrity": "sha512-3qK2zb5EwCwxnO2HeO+TRqCubeI/NgCe+kL5dTJlPldV/uwCnUgC7VbEzgmxbfrkbjehL4H9BPztWOEtsoMwew==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@next/swc-linux-arm64-gnu": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/@next/swc-linux-arm64-gnu/-/swc-linux-arm64-gnu-15.2.4.tgz",
            "integrity": "sha512-HFN6GKUcrTWvem8AZN7tT95zPb0GUGv9v0d0iyuTb303vbXkkbHDp/DxufB04jNVD+IN9yHy7y/6Mqq0h0YVaQ==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@next/swc-linux-arm64-musl": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/@next/swc-linux-arm64-musl/-/swc-linux-arm64-musl-15.2.4.tgz",
            "integrity": "sha512-Oioa0SORWLwi35/kVB8aCk5Uq+5/ZIumMK1kJV+jSdazFm2NzPDztsefzdmzzpx5oGCJ6FkUC7vkaUseNTStNA==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@next/swc-linux-x64-gnu": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/@next/swc-linux-x64-gnu/-/swc-linux-x64-gnu-15.2.4.tgz",
            "integrity": "sha512-yb5WTRaHdkgOqFOZiu6rHV1fAEK0flVpaIN2HB6kxHVSy/dIajWbThS7qON3W9/SNOH2JWkVCyulgGYekMePuw==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@next/swc-linux-x64-musl": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/@next/swc-linux-x64-musl/-/swc-linux-x64-musl-15.2.4.tgz",
            "integrity": "sha512-Dcdv/ix6srhkM25fgXiyOieFUkz+fOYkHlydWCtB0xMST6X9XYI3yPDKBZt1xuhOytONsIFJFB08xXYsxUwJLw==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "linux"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@next/swc-win32-arm64-msvc": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/@next/swc-win32-arm64-msvc/-/swc-win32-arm64-msvc-15.2.4.tgz",
            "integrity": "sha512-dW0i7eukvDxtIhCYkMrZNQfNicPDExt2jPb9AZPpL7cfyUo7QSNl1DjsHjmmKp6qNAqUESyT8YFl/Aw91cNJJg==",
            "cpu": [
                "arm64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@next/swc-win32-x64-msvc": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/@next/swc-win32-x64-msvc/-/swc-win32-x64-msvc-15.2.4.tgz",
            "integrity": "sha512-SbnWkJmkS7Xl3kre8SdMF6F/XDh1DTFEhp0jRTj/uB8iPKoU2bb2NDfcu+iifv1+mxQEd1g2vvSxcZbXSKyWiQ==",
            "cpu": [
                "x64"
            ],
            "license": "MIT",
            "optional": true,
            "os": [
                "win32"
            ],
            "engines": {
                "node": ">= 10"
            }
        },
        "node_modules/@nodelib/fs.scandir": {
            "version": "2.1.5",
            "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz",
            "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@nodelib/fs.stat": "2.0.5",
                "run-parallel": "^1.1.9"
            },
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/@nodelib/fs.stat": {
            "version": "2.0.5",
            "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz",
            "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/@nodelib/fs.walk": {
            "version": "1.2.8",
            "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz",
            "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@nodelib/fs.scandir": "2.1.5",
                "fastq": "^1.6.0"
            },
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/@pkgjs/parseargs": {
            "version": "0.11.0",
            "resolved": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz",
            "integrity": "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==",
            "dev": true,
            "license": "MIT",
            "optional": true,
            "engines": {
                "node": ">=14"
            }
        },
        "node_modules/@radix-ui/number": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/number/-/number-1.1.0.tgz",
            "integrity": "sha512-V3gRzhVNU1ldS5XhAPTom1fOIo4ccrjjJgmE+LI2h/WaFpHmx0MQApT+KZHnx8abG6Avtfcz4WoEciMnpFT3HQ==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/primitive": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.1.tgz",
            "integrity": "sha512-SJ31y+Q/zAyShtXJc8x83i9TYdbAfHZ++tUZnvjJJqFjzsdUnKsxPL6IEtBlxKkU7yzer//GQtZSV4GbldL3YA==",
            "license": "MIT"
        },
        "node_modules/@radix-ui/react-accordion": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.2.tgz",
            "integrity": "sha512-b1oh54x4DMCdGsB4/7ahiSrViXxaBwRPotiZNnYXjLha9vfuURSAZErki6qjDoSIV0eXx5v57XnTGVtGwnfp2g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collapsible": "1.1.2",
                "@radix-ui/react-collection": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-alert-dialog": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.4.tgz",
            "integrity": "sha512-A6Kh23qZDLy3PSU4bh2UJZznOrUdHImIXqF8YtUa6CN73f8EOO9XlXSCd9IHyPvIquTaa/kwaSWzZTtUvgXVGw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-dialog": "1.1.4",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-slot": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-arrow": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.1.tgz",
            "integrity": "sha512-NaVpZfmv8SKeZbn4ijN2V3jlHA9ngBG16VnIIm22nUR0Yk8KUALyBxT3KYEUnNuch9sTE8UTsS3whzBgKOL30w==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-aspect-ratio": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-aspect-ratio/-/react-aspect-ratio-1.1.1.tgz",
            "integrity": "sha512-kNU4FIpcFMBLkOUcgeIteH06/8JLBcYY6Le1iKenDGCYNYFX3TQqCZjzkOsz37h7r94/99GTb7YhEr98ZBJibw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-avatar": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.2.tgz",
            "integrity": "sha512-GaC7bXQZ5VgZvVvsJ5mu/AEbjYLnhhkoidOboC50Z6FFlLA03wG2ianUoH+zgDQ31/9gCF59bE4+2bBgTyMiig==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-checkbox": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.3.tgz",
            "integrity": "sha512-HD7/ocp8f1B3e6OHygH0n7ZKjONkhciy1Nh0yuBgObqThc3oyx+vuMfFHKAknXRHHWVE9XvXStxJFyjUmB8PIw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-previous": "1.1.0",
                "@radix-ui/react-use-size": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collapsible": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.2.tgz",
            "integrity": "sha512-PliMB63vxz7vggcyq0IxNYk8vGDrLXVWw4+W4B8YnwI1s18x7YZYqlG9PLX7XxAJUi0g2DxP4XKJMFHh/iVh9A==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-collection": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.1.tgz",
            "integrity": "sha512-LwT3pSho9Dljg+wY2KN2mrrh6y3qELfftINERIzBUO9e0N+t0oMTyn3k9iv+ZqgrwGkRnLpNJrsMv9BZlt2yuA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-slot": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.1.tgz",
            "integrity": "sha512-Y9VzoRDSJtgFMUCoiZBDVo084VQ5hfpXxVE+NgkdNsjiDBByiImMZKKhxMwCbdHvhlENG6a833CbFkOQvTricw==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-context": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz",
            "integrity": "sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-context-menu": {
            "version": "2.2.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context-menu/-/react-context-menu-2.2.4.tgz",
            "integrity": "sha512-ap4wdGwK52rJxGkwukU1NrnEodsUFQIooANKu+ey7d6raQ2biTcEf8za1zr0mgFHieevRTB2nK4dJeN8pTAZGQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-menu": "2.1.4",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-dialog": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.4.tgz",
            "integrity": "sha512-Ur7EV1IwQGCyaAuyDRiOLA5JIUZxELJljF+MbM/2NC0BYwfuRrbpS30BiQBJrVruscgUkieKkqXYDOoByaxIoA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-dismissable-layer": "1.1.3",
                "@radix-ui/react-focus-guards": "1.1.1",
                "@radix-ui/react-focus-scope": "1.1.1",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-portal": "1.1.3",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-slot": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "aria-hidden": "^1.1.1",
                "react-remove-scroll": "^2.6.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-direction": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.0.tgz",
            "integrity": "sha512-BUuBvgThEiAXh2DWu93XsT+a3aWrGqolGlqqw5VU1kG7p/ZH2cuDlM1sRLNnY3QcBS69UIz2mcKhMxDsdewhjg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-dismissable-layer": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.3.tgz",
            "integrity": "sha512-onrWn/72lQoEucDmJnr8uczSNTujT0vJnA/X5+3AkChVPowr8n1yvIKIabhWyMQeMvvmdpsvcyDqx3X1LEXCPg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-escape-keydown": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-dropdown-menu": {
            "version": "2.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-dropdown-menu/-/react-dropdown-menu-2.1.4.tgz",
            "integrity": "sha512-iXU1Ab5ecM+yEepGAWK8ZhMyKX4ubFdCNtol4sT9D0OVErG9PNElfx3TQhjw7n7BC5nFVz68/5//clWy+8TXzA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-menu": "2.1.4",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-focus-guards": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.1.tgz",
            "integrity": "sha512-pSIwfrT1a6sIoDASCSpFwOasEwKTZWDw/iBdtnqKO7v6FeOzYJ7U53cPzYFVR3geGGXgVHaH+CdngrrAzqUGxg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-focus-scope": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.1.tgz",
            "integrity": "sha512-01omzJAYRxXdG2/he/+xy+c8a8gCydoQ1yOxnWNcRhrrBW5W+RQJ22EK1SaO8tb3WoUsuEw7mJjBozPzihDFjA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-callback-ref": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-hover-card": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-hover-card/-/react-hover-card-1.1.4.tgz",
            "integrity": "sha512-QSUUnRA3PQ2UhvoCv3eYvMnCAgGQW+sTu86QPuNb+ZMi+ZENd6UWpiXbcWDQ4AEaKF9KKpCHBeaJz9Rw6lRlaQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-dismissable-layer": "1.1.3",
                "@radix-ui/react-popper": "1.2.1",
                "@radix-ui/react-portal": "1.1.3",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-id": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.0.tgz",
            "integrity": "sha512-EJUrI8yYh7WOjNOqpoJaf1jlFIH2LvtgAl+YcFqNCa+4hj64ZXmPkAKOFs/ukjz3byN6bdb/AVUqHkI8/uWWMA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-label": {
            "version": "2.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.1.tgz",
            "integrity": "sha512-UUw5E4e/2+4kFMH7+YxORXGWggtY6sM8WIwh5RZchhLuUg2H1hc98Py+pr8HMz6rdaYrK2t296ZEjYLOCO5uUw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-menu": {
            "version": "2.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-menu/-/react-menu-2.1.4.tgz",
            "integrity": "sha512-BnOgVoL6YYdHAG6DtXONaR29Eq4nvbi8rutrV/xlr3RQCMMb3yqP85Qiw/3NReozrSW+4dfLkK+rc1hb4wPU/A==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-dismissable-layer": "1.1.3",
                "@radix-ui/react-focus-guards": "1.1.1",
                "@radix-ui/react-focus-scope": "1.1.1",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-popper": "1.2.1",
                "@radix-ui/react-portal": "1.1.3",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-roving-focus": "1.1.1",
                "@radix-ui/react-slot": "1.1.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "aria-hidden": "^1.1.1",
                "react-remove-scroll": "^2.6.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-menubar": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.4.tgz",
            "integrity": "sha512-+KMpi7VAZuB46+1LD7a30zb5IxyzLgC8m8j42gk3N4TUCcViNQdX8FhoH1HDvYiA8quuqcek4R4bYpPn/SY1GA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-menu": "2.1.4",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-roving-focus": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-navigation-menu": {
            "version": "1.2.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.3.tgz",
            "integrity": "sha512-IQWAsQ7dsLIYDrn0WqPU+cdM7MONTv9nqrLVYoie3BPiabSfUVDe6Fr+oEt0Cofsr9ONDcDe9xhmJbL1Uq1yKg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-dismissable-layer": "1.1.3",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0",
                "@radix-ui/react-use-previous": "1.1.0",
                "@radix-ui/react-visually-hidden": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-popover": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-popover/-/react-popover-1.1.4.tgz",
            "integrity": "sha512-aUACAkXx8LaFymDma+HQVji7WhvEhpFJ7+qPz17Nf4lLZqtreGOFRiNQWQmhzp7kEWg9cOyyQJpdIMUMPc/CPw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-dismissable-layer": "1.1.3",
                "@radix-ui/react-focus-guards": "1.1.1",
                "@radix-ui/react-focus-scope": "1.1.1",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-popper": "1.2.1",
                "@radix-ui/react-portal": "1.1.3",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-slot": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "aria-hidden": "^1.1.1",
                "react-remove-scroll": "^2.6.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-popper": {
            "version": "1.2.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.1.tgz",
            "integrity": "sha512-3kn5Me69L+jv82EKRuQCXdYyf1DqHwD2U/sxoNgBGCB7K9TRc3bQamQ+5EPM9EvyPdli0W41sROd+ZU1dTCztw==",
            "license": "MIT",
            "dependencies": {
                "@floating-ui/react-dom": "^2.0.0",
                "@radix-ui/react-arrow": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0",
                "@radix-ui/react-use-rect": "1.1.0",
                "@radix-ui/react-use-size": "1.1.0",
                "@radix-ui/rect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-portal": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.3.tgz",
            "integrity": "sha512-NciRqhXnGojhT93RPyDaMPfLH3ZSl4jjIFbZQ1b/vxvZEdHsBZ49wP9w8L3HzUQwep01LcWtkUvm0OVB5JAHTw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-presence": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.2.tgz",
            "integrity": "sha512-18TFr80t5EVgL9x1SwF/YGtfG+l0BS0PRAlCWBDoBEiDQjeKgnNZRVJp/oVBl24sr3Gbfwc/Qpj4OcWTQMsAEg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-primitive": {
            "version": "2.0.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.1.tgz",
            "integrity": "sha512-sHCWTtxwNn3L3fH8qAfnF3WbUZycW93SM1j3NFDzXBiz8D6F5UTTy8G1+WFEaiCdvCVRJWj6N2R4Xq6HdiHmDg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-progress": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-progress/-/react-progress-1.1.1.tgz",
            "integrity": "sha512-6diOawA84f/eMxFHcWut0aE1C2kyE9dOyCTQOMRR2C/qPiXz/X0SaiA/RLbapQaXUCmy0/hLMf9meSccD1N0pA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-primitive": "2.0.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-radio-group": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-radio-group/-/react-radio-group-1.2.2.tgz",
            "integrity": "sha512-E0MLLGfOP0l8P/NxgVzfXJ8w3Ch8cdO6UDzJfDChu4EJDy+/WdO5LqpdY8PYnCErkmZH3gZhDL1K7kQ41fAHuQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-roving-focus": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-previous": "1.1.0",
                "@radix-ui/react-use-size": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-roving-focus": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.1.tgz",
            "integrity": "sha512-QE1RoxPGJ/Nm8Qmk0PxP8ojmoaS67i0s7hVssS7KuI2FQoc/uzVlZsqKfQvxPE6D8hICCPHJ4D88zNhT3OOmkw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-scroll-area": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.2.tgz",
            "integrity": "sha512-EFI1N/S3YxZEW/lJ/H1jY3njlvTd8tBmgKEn4GHi51+aMm94i6NmAJstsm5cu3yJwYqYc93gpCPm21FeAbFk6g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/number": "1.1.0",
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-select": {
            "version": "2.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-select/-/react-select-2.1.4.tgz",
            "integrity": "sha512-pOkb2u8KgO47j/h7AylCj7dJsm69BXcjkrvTqMptFqsE2i0p8lHkfgneXKjAgPzBMivnoMyt8o4KiV4wYzDdyQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/number": "1.1.0",
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-dismissable-layer": "1.1.3",
                "@radix-ui/react-focus-guards": "1.1.1",
                "@radix-ui/react-focus-scope": "1.1.1",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-popper": "1.2.1",
                "@radix-ui/react-portal": "1.1.3",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-slot": "1.1.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0",
                "@radix-ui/react-use-previous": "1.1.0",
                "@radix-ui/react-visually-hidden": "1.1.1",
                "aria-hidden": "^1.1.1",
                "react-remove-scroll": "^2.6.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-separator": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-separator/-/react-separator-1.1.1.tgz",
            "integrity": "sha512-RRiNRSrD8iUiXriq/Y5n4/3iE8HzqgLHsusUSg5jVpU2+3tqcUFPJXHDymwEypunc2sWxDUS3UC+rkZRlHedsw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slider": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.2.tgz",
            "integrity": "sha512-sNlU06ii1/ZcbHf8I9En54ZPW0Vil/yPVg4vQMcFNjrIx51jsHbFl1HYHQvCIWJSr1q0ZmA+iIs/ZTv8h7HHSA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/number": "1.1.0",
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0",
                "@radix-ui/react-use-previous": "1.1.0",
                "@radix-ui/react-use-size": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-slot": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.1.1.tgz",
            "integrity": "sha512-RApLLOcINYJA+dMVbOju7MYv1Mb2EBp2nH4HdDzXTSyaR5optlm6Otrz1euW3HbdOR8UmmFK06TD+A9frYWv+g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-switch": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.2.tgz",
            "integrity": "sha512-zGukiWHjEdBCRyXvKR6iXAQG6qXm2esuAD6kDOi9Cn+1X6ev3ASo4+CsYaD6Fov9r/AQFekqnD/7+V0Cs6/98g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-previous": "1.1.0",
                "@radix-ui/react-use-size": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tabs": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.2.tgz",
            "integrity": "sha512-9u/tQJMcC2aGq7KXpGivMm1mgq7oRJKXphDwdypPd/j21j/2znamPU8WkXgnhUaTrSFNIt8XhOyCAupg8/GbwQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-roving-focus": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toast": {
            "version": "1.2.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-toast/-/react-toast-1.2.4.tgz",
            "integrity": "sha512-Sch9idFJHJTMH9YNpxxESqABcAFweJG4tKv+0zo0m5XBvUSL8FM5xKcJLFLXononpePs8IclyX1KieL5SDUNgA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-collection": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-dismissable-layer": "1.1.3",
                "@radix-ui/react-portal": "1.1.3",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-callback-ref": "1.1.0",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-use-layout-effect": "1.1.0",
                "@radix-ui/react-visually-hidden": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toggle": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.1.tgz",
            "integrity": "sha512-i77tcgObYr743IonC1hrsnnPmszDRn8p+EGUsUt+5a/JFn28fxaM88Py6V2mc8J5kELMWishI0rLnuGLFD/nnQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-toggle-group": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.1.tgz",
            "integrity": "sha512-OgDLZEA30Ylyz8YSXvnGqIHtERqnUt1KUYTKdw/y8u7Ci6zGiJfXc02jahmcSNK3YcErqioj/9flWC9S1ihfwg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-direction": "1.1.0",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-roving-focus": "1.1.1",
                "@radix-ui/react-toggle": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-tooltip": {
            "version": "1.1.6",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-tooltip/-/react-tooltip-1.1.6.tgz",
            "integrity": "sha512-TLB5D8QLExS1uDn7+wH/bjEmRurNMTzNrtq7IjaS4kjion9NtzsTGkvR5+i7yc9q01Pi2KMM2cN3f8UG4IvvXA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.1",
                "@radix-ui/react-compose-refs": "1.1.1",
                "@radix-ui/react-context": "1.1.1",
                "@radix-ui/react-dismissable-layer": "1.1.3",
                "@radix-ui/react-id": "1.1.0",
                "@radix-ui/react-popper": "1.2.1",
                "@radix-ui/react-portal": "1.1.3",
                "@radix-ui/react-presence": "1.1.2",
                "@radix-ui/react-primitive": "2.0.1",
                "@radix-ui/react-slot": "1.1.1",
                "@radix-ui/react-use-controllable-state": "1.1.0",
                "@radix-ui/react-visually-hidden": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-callback-ref": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0.tgz",
            "integrity": "sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-controllable-state": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.1.0.tgz",
            "integrity": "sha512-MtfMVJiSr2NjzS0Aa90NPTnvTSg6C/JLCV7ma0W6+OMV78vd8OyRpID+Ng9LxzsPbLeuBnWBA1Nq30AtBIDChw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-callback-ref": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-effect-event": {
            "version": "0.0.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz",
            "integrity": "sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-effect-event/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-escape-keydown": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0.tgz",
            "integrity": "sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-callback-ref": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.0.tgz",
            "integrity": "sha512-+FPE0rOdziWSrH9athwI1R0HDVbWlEhd+FR+aSDk4uWGmSJ9Z54sdZVDQPZAinJhJXwfT+qnj969mCsT2gfm5w==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-previous": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0.tgz",
            "integrity": "sha512-Z/e78qg2YFnnXcW88A4JmTtm4ADckLno6F7OXotmkQfeuCVaKuYzqAATPhVzl3delXE7CxIV8shofPn3jPc5Og==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-rect": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0.tgz",
            "integrity": "sha512-0Fmkebhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/rect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-use-size": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.0.tgz",
            "integrity": "sha512-XW3/vWuIXHa+2Uwcc2ABSfcCledmXhhQPlGbfcRXbiUQI5Icjcg19BGCZVKKInYbvUCut/ufbbLLPFC5cbb1hw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.0"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/react-visually-hidden": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.1.1.tgz",
            "integrity": "sha512-vVfA2IZ9q/J+gEamvj761Oq1FpWgCDaNOOIfbPVp2MVPLEomUr5+Vf7kJGwQ24YxZSlQVar7Bes8kyTo5Dshpg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.0.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/@radix-ui/rect": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.0.tgz",
            "integrity": "sha512-A9+lCBZoaMJlVKcRBz2YByCG+Cp2t6nAnMnNba+XiWxnj6r4JUFqfsgwocMBZU9LPtdxC6wB56ySYpc7LQIoJg==",
            "license": "MIT"
        },
        "node_modules/@standard-schema/utils": {
            "version": "0.3.0",
            "resolved": "https://registry.npmjs.org/@standard-schema/utils/-/utils-0.3.0.tgz",
            "integrity": "sha512-e7Mew686owMaPJVNNLs55PUvgz371nKgwsc4vxE49zsODpJEnxgxRo2y/OKrqueavXgZNMDVj3DdHFlaSAeU8g==",
            "license": "MIT"
        },
        "node_modules/@supabase/auth-helpers-nextjs": {
            "version": "0.10.0",
            "resolved": "https://registry.npmjs.org/@supabase/auth-helpers-nextjs/-/auth-helpers-nextjs-0.10.0.tgz",
            "integrity": "sha512-2dfOGsM4yZt0oS4TPiE7bD4vf7EVz7NRz/IJrV6vLg0GP7sMUx8wndv2euLGq4BjN9lUCpu6DG/uCC8j+ylwPg==",
            "deprecated": "This package is now deprecated - please use the @supabase/ssr package instead.",
            "license": "MIT",
            "dependencies": {
                "@supabase/auth-helpers-shared": "0.7.0",
                "set-cookie-parser": "^2.6.0"
            },
            "peerDependencies": {
                "@supabase/supabase-js": "^2.39.8"
            }
        },
        "node_modules/@supabase/auth-helpers-shared": {
            "version": "0.7.0",
            "resolved": "https://registry.npmjs.org/@supabase/auth-helpers-shared/-/auth-helpers-shared-0.7.0.tgz",
            "integrity": "sha512-FBFf2ei2R7QC+B/5wWkthMha8Ca2bWHAndN+syfuEUUfufv4mLcAgBCcgNg5nJR8L0gZfyuaxgubtOc9aW3Cpg==",
            "deprecated": "This package is now deprecated - please use the @supabase/ssr package instead.",
            "license": "MIT",
            "dependencies": {
                "jose": "^4.14.4"
            },
            "peerDependencies": {
                "@supabase/supabase-js": "^2.39.8"
            }
        },
        "node_modules/@supabase/auth-js": {
            "version": "2.70.0",
            "resolved": "https://registry.npmjs.org/@supabase/auth-js/-/auth-js-2.70.0.tgz",
            "integrity": "sha512-BaAK/tOAZFJtzF1sE3gJ2FwTjLf4ky3PSvcvLGEgEmO4BSBkwWKu8l67rLLIBZPDnCyV7Owk2uPyKHa0kj5QGg==",
            "license": "MIT",
            "dependencies": {
                "@supabase/node-fetch": "^2.6.14"
            }
        },
        "node_modules/@supabase/functions-js": {
            "version": "2.4.4",
            "resolved": "https://registry.npmjs.org/@supabase/functions-js/-/functions-js-2.4.4.tgz",
            "integrity": "sha512-WL2p6r4AXNGwop7iwvul2BvOtuJ1YQy8EbOd0dhG1oN1q8el/BIRSFCFnWAMM/vJJlHWLi4ad22sKbKr9mvjoA==",
            "license": "MIT",
            "dependencies": {
                "@supabase/node-fetch": "^2.6.14"
            }
        },
        "node_modules/@supabase/node-fetch": {
            "version": "2.6.15",
            "resolved": "https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz",
            "integrity": "sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==",
            "license": "MIT",
            "dependencies": {
                "whatwg-url": "^5.0.0"
            },
            "engines": {
                "node": "4.x || >=6.0.0"
            }
        },
        "node_modules/@supabase/postgrest-js": {
            "version": "1.19.4",
            "resolved": "https://registry.npmjs.org/@supabase/postgrest-js/-/postgrest-js-1.19.4.tgz",
            "integrity": "sha512-O4soKqKtZIW3olqmbXXbKugUtByD2jPa8kL2m2c1oozAO11uCcGrRhkZL0kVxjBLrXHE0mdSkFsMj7jDSfyNpw==",
            "license": "MIT",
            "dependencies": {
                "@supabase/node-fetch": "^2.6.14"
            }
        },
        "node_modules/@supabase/realtime-js": {
            "version": "2.11.10",
            "resolved": "https://registry.npmjs.org/@supabase/realtime-js/-/realtime-js-2.11.10.tgz",
            "integrity": "sha512-SJKVa7EejnuyfImrbzx+HaD9i6T784khuw1zP+MBD7BmJYChegGxYigPzkKX8CK8nGuDntmeSD3fvriaH0EGZA==",
            "license": "MIT",
            "dependencies": {
                "@supabase/node-fetch": "^2.6.13",
                "@types/phoenix": "^1.6.6",
                "@types/ws": "^8.18.1",
                "ws": "^8.18.2"
            }
        },
        "node_modules/@supabase/ssr": {
            "version": "0.6.1",
            "resolved": "https://registry.npmjs.org/@supabase/ssr/-/ssr-0.6.1.tgz",
            "integrity": "sha512-QtQgEMvaDzr77Mk3vZ3jWg2/y+D8tExYF7vcJT+wQ8ysuvOeGGjYbZlvj5bHYsj/SpC0bihcisnwPrM4Gp5G4g==",
            "license": "MIT",
            "dependencies": {
                "cookie": "^1.0.1"
            },
            "peerDependencies": {
                "@supabase/supabase-js": "^2.43.4"
            }
        },
        "node_modules/@supabase/storage-js": {
            "version": "2.7.1",
            "resolved": "https://registry.npmjs.org/@supabase/storage-js/-/storage-js-2.7.1.tgz",
            "integrity": "sha512-asYHcyDR1fKqrMpytAS1zjyEfvxuOIp1CIXX7ji4lHHcJKqyk+sLl/Vxgm4sN6u8zvuUtae9e4kDxQP2qrwWBA==",
            "license": "MIT",
            "dependencies": {
                "@supabase/node-fetch": "^2.6.14"
            }
        },
        "node_modules/@supabase/supabase-js": {
            "version": "2.50.0",
            "resolved": "https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.50.0.tgz",
            "integrity": "sha512-M1Gd5tPaaghYZ9OjeO1iORRqbTWFEz/cF3pPubRnMPzA+A8SiUsXXWDP+DWsASZcjEcVEcVQIAF38i5wrijYOg==",
            "license": "MIT",
            "dependencies": {
                "@supabase/auth-js": "2.70.0",
                "@supabase/functions-js": "2.4.4",
                "@supabase/node-fetch": "2.6.15",
                "@supabase/postgrest-js": "1.19.4",
                "@supabase/realtime-js": "2.11.10",
                "@supabase/storage-js": "2.7.1"
            }
        },
        "node_modules/@swc/counter": {
            "version": "0.1.3",
            "resolved": "https://registry.npmjs.org/@swc/counter/-/counter-0.1.3.tgz",
            "integrity": "sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==",
            "license": "Apache-2.0"
        },
        "node_modules/@swc/helpers": {
            "version": "0.5.15",
            "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.15.tgz",
            "integrity": "sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==",
            "license": "Apache-2.0",
            "dependencies": {
                "tslib": "^2.8.0"
            }
        },
        "node_modules/@types/d3-array": {
            "version": "3.2.1",
            "resolved": "https://registry.npmjs.org/@types/d3-array/-/d3-array-3.2.1.tgz",
            "integrity": "sha512-Y2Jn2idRrLzUfAKV2LyRImR+y4oa2AntrgID95SHJxuMUrkNXmanDSed71sRNZysveJVt1hLLemQZIady0FpEg==",
            "license": "MIT"
        },
        "node_modules/@types/d3-color": {
            "version": "3.1.3",
            "resolved": "https://registry.npmjs.org/@types/d3-color/-/d3-color-3.1.3.tgz",
            "integrity": "sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==",
            "license": "MIT"
        },
        "node_modules/@types/d3-ease": {
            "version": "3.0.2",
            "resolved": "https://registry.npmjs.org/@types/d3-ease/-/d3-ease-3.0.2.tgz",
            "integrity": "sha512-NcV1JjO5oDzoK26oMzbILE6HW7uVXOHLQvHshBUW4UMdZGfiY6v5BeQwh9a9tCzv+CeefZQHJt5SRgK154RtiA==",
            "license": "MIT"
        },
        "node_modules/@types/d3-interpolate": {
            "version": "3.0.4",
            "resolved": "https://registry.npmjs.org/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz",
            "integrity": "sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==",
            "license": "MIT",
            "dependencies": {
                "@types/d3-color": "*"
            }
        },
        "node_modules/@types/d3-path": {
            "version": "3.1.1",
            "resolved": "https://registry.npmjs.org/@types/d3-path/-/d3-path-3.1.1.tgz",
            "integrity": "sha512-VMZBYyQvbGmWyWVea0EHs/BwLgxc+MKi1zLDCONksozI4YJMcTt8ZEuIR4Sb1MMTE8MMW49v0IwI5+b7RmfWlg==",
            "license": "MIT"
        },
        "node_modules/@types/d3-scale": {
            "version": "4.0.9",
            "resolved": "https://registry.npmjs.org/@types/d3-scale/-/d3-scale-4.0.9.tgz",
            "integrity": "sha512-dLmtwB8zkAeO/juAMfnV+sItKjlsw2lKdZVVy6LRr0cBmegxSABiLEpGVmSJJ8O08i4+sGR6qQtb6WtuwJdvVw==",
            "license": "MIT",
            "dependencies": {
                "@types/d3-time": "*"
            }
        },
        "node_modules/@types/d3-shape": {
            "version": "3.1.7",
            "resolved": "https://registry.npmjs.org/@types/d3-shape/-/d3-shape-3.1.7.tgz",
            "integrity": "sha512-VLvUQ33C+3J+8p+Daf+nYSOsjB4GXp19/S/aGo60m9h1v6XaxjiT82lKVWJCfzhtuZ3yD7i/TPeC/fuKLLOSmg==",
            "license": "MIT",
            "dependencies": {
                "@types/d3-path": "*"
            }
        },
        "node_modules/@types/d3-time": {
            "version": "3.0.4",
            "resolved": "https://registry.npmjs.org/@types/d3-time/-/d3-time-3.0.4.tgz",
            "integrity": "sha512-yuzZug1nkAAaBlBBikKZTgzCeA+k1uy4ZFwWANOfKw5z5LRhV0gNA7gNkKm7HoK+HRN0wX3EkxGk0fpbWhmB7g==",
            "license": "MIT"
        },
        "node_modules/@types/d3-timer": {
            "version": "3.0.2",
            "resolved": "https://registry.npmjs.org/@types/d3-timer/-/d3-timer-3.0.2.tgz",
            "integrity": "sha512-Ps3T8E8dZDam6fUyNiMkekK3XUsaUEik+idO9/YjPtfj2qruF8tFBXS7XhtE4iIXBLxhmLjP3SXpLhVf21I9Lw==",
            "license": "MIT"
        },
        "node_modules/@types/node": {
            "version": "22.15.32",
            "resolved": "https://registry.npmjs.org/@types/node/-/node-22.15.32.tgz",
            "integrity": "sha512-3jigKqgSjsH6gYZv2nEsqdXfZqIFGAV36XYYjf9KGZ3PSG+IhLecqPnI310RvjutyMwifE2hhhNEklOUrvx/wA==",
            "license": "MIT",
            "dependencies": {
                "undici-types": "~6.21.0"
            }
        },
        "node_modules/@types/phoenix": {
            "version": "1.6.6",
            "resolved": "https://registry.npmjs.org/@types/phoenix/-/phoenix-1.6.6.tgz",
            "integrity": "sha512-PIzZZlEppgrpoT2QgbnDU+MMzuR6BbCjllj0bM70lWoejMeNJAxCchxnv7J3XFkI8MpygtRpzXrIlmWUBclP5A==",
            "license": "MIT"
        },
        "node_modules/@types/react": {
            "version": "19.1.8",
            "resolved": "https://registry.npmjs.org/@types/react/-/react-19.1.8.tgz",
            "integrity": "sha512-AwAfQ2Wa5bCx9WP8nZL2uMZWod7J7/JSplxbTmBQ5ms6QpqNYm672H0Vu9ZVKVngQ+ii4R/byguVEUZQyeg44g==",
            "dev": true,
            "devOptional": true,
            "license": "MIT",
            "dependencies": {
                "csstype": "^3.0.2"
            }
        },
        "node_modules/@types/react-dom": {
            "version": "19.1.6",
            "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-19.1.6.tgz",
            "integrity": "sha512-4hOiT/dwO8Ko0gV1m/TJZYk3y0KBnY9vzDh7W+DH17b2HFSOGgdj33dhihPeuy3l0q23+4e+hoXHV6hCC4dCXw==",
            "dev": true,
            "devOptional": true,
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "^19.0.0"
            }
        },
        "node_modules/@types/ws": {
            "version": "8.18.1",
            "resolved": "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz",
            "integrity": "sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==",
            "license": "MIT",
            "dependencies": {
                "@types/node": "*"
            }
        },
        "node_modules/ansi-regex": {
            "version": "6.1.0",
            "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz",
            "integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=12"
            },
            "funding": {
                "url": "https://github.com/chalk/ansi-regex?sponsor=1"
            }
        },
        "node_modules/ansi-styles": {
            "version": "6.2.1",
            "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz",
            "integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=12"
            },
            "funding": {
                "url": "https://github.com/chalk/ansi-styles?sponsor=1"
            }
        },
        "node_modules/any-promise": {
            "version": "1.3.0",
            "resolved": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz",
            "integrity": "sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/anymatch": {
            "version": "3.1.3",
            "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz",
            "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "normalize-path": "^3.0.0",
                "picomatch": "^2.0.4"
            },
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/arg": {
            "version": "5.0.2",
            "resolved": "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz",
            "integrity": "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/aria-hidden": {
            "version": "1.2.6",
            "resolved": "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.6.tgz",
            "integrity": "sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA==",
            "license": "MIT",
            "dependencies": {
                "tslib": "^2.0.0"
            },
            "engines": {
                "node": ">=10"
            }
        },
        "node_modules/autoprefixer": {
            "version": "10.4.21",
            "resolved": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz",
            "integrity": "sha512-O+A6LWV5LDHSJD3LjHYoNi4VLsj/Whi7k6zG12xTYaU4cQ8oxQGckXNX8cRHK5yOZ/ppVHe0ZBXGzSV9jXdVbQ==",
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/postcss/"
                },
                {
                    "type": "tidelift",
                    "url": "https://tidelift.com/funding/github/npm/autoprefixer"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "browserslist": "^4.24.4",
                "caniuse-lite": "^1.0.30001702",
                "fraction.js": "^4.3.7",
                "normalize-range": "^0.1.2",
                "picocolors": "^1.1.1",
                "postcss-value-parser": "^4.2.0"
            },
            "bin": {
                "autoprefixer": "bin/autoprefixer"
            },
            "engines": {
                "node": "^10 || ^12 || >=14"
            },
            "peerDependencies": {
                "postcss": "^8.1.0"
            }
        },
        "node_modules/balanced-match": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz",
            "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/binary-extensions": {
            "version": "2.3.0",
            "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz",
            "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=8"
            },
            "funding": {
                "url": "https://github.com/sponsors/sindresorhus"
            }
        },
        "node_modules/brace-expansion": {
            "version": "2.0.2",
            "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz",
            "integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "balanced-match": "^1.0.0"
            }
        },
        "node_modules/braces": {
            "version": "3.0.3",
            "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz",
            "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "fill-range": "^7.1.1"
            },
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/browserslist": {
            "version": "4.25.0",
            "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz",
            "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==",
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/browserslist"
                },
                {
                    "type": "tidelift",
                    "url": "https://tidelift.com/funding/github/npm/browserslist"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "caniuse-lite": "^1.0.30001718",
                "electron-to-chromium": "^1.5.160",
                "node-releases": "^2.0.19",
                "update-browserslist-db": "^1.1.3"
            },
            "bin": {
                "browserslist": "cli.js"
            },
            "engines": {
                "node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"
            }
        },
        "node_modules/busboy": {
            "version": "1.6.0",
            "resolved": "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz",
            "integrity": "sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==",
            "dependencies": {
                "streamsearch": "^1.1.0"
            },
            "engines": {
                "node": ">=10.16.0"
            }
        },
        "node_modules/camelcase-css": {
            "version": "2.0.1",
            "resolved": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz",
            "integrity": "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 6"
            }
        },
        "node_modules/caniuse-lite": {
            "version": "1.0.30001724",
            "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001724.tgz",
            "integrity": "sha512-WqJo7p0TbHDOythNTqYujmaJTvtYRZrjpP8TCvH6Vb9CYJerJNKamKzIWOM4BkQatWj9H2lYulpdAQNBe7QhNA==",
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/browserslist"
                },
                {
                    "type": "tidelift",
                    "url": "https://tidelift.com/funding/github/npm/caniuse-lite"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "CC-BY-4.0"
        },
        "node_modules/chart.js": {
            "version": "4.5.0",
            "resolved": "https://registry.npmjs.org/chart.js/-/chart.js-4.5.0.tgz",
            "integrity": "sha512-aYeC/jDgSEx8SHWZvANYMioYMZ2KX02W6f6uVfyteuCGcadDLcYVHdfdygsTQkQ4TKn5lghoojAsPj5pu0SnvQ==",
            "license": "MIT",
            "dependencies": {
                "@kurkle/color": "^0.3.0"
            },
            "engines": {
                "pnpm": ">=8"
            }
        },
        "node_modules/chokidar": {
            "version": "3.6.0",
            "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz",
            "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "anymatch": "~3.1.2",
                "braces": "~3.0.2",
                "glob-parent": "~5.1.2",
                "is-binary-path": "~2.1.0",
                "is-glob": "~4.0.1",
                "normalize-path": "~3.0.0",
                "readdirp": "~3.6.0"
            },
            "engines": {
                "node": ">= 8.10.0"
            },
            "funding": {
                "url": "https://paulmillr.com/funding/"
            },
            "optionalDependencies": {
                "fsevents": "~2.3.2"
            }
        },
        "node_modules/chokidar/node_modules/glob-parent": {
            "version": "5.1.2",
            "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",
            "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "is-glob": "^4.0.1"
            },
            "engines": {
                "node": ">= 6"
            }
        },
        "node_modules/class-variance-authority": {
            "version": "0.7.1",
            "resolved": "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz",
            "integrity": "sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==",
            "license": "Apache-2.0",
            "dependencies": {
                "clsx": "^2.1.1"
            },
            "funding": {
                "url": "https://polar.sh/cva"
            }
        },
        "node_modules/client-only": {
            "version": "0.0.1",
            "resolved": "https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz",
            "integrity": "sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==",
            "license": "MIT"
        },
        "node_modules/clsx": {
            "version": "2.1.1",
            "resolved": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz",
            "integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==",
            "license": "MIT",
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/cmdk": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/cmdk/-/cmdk-1.1.1.tgz",
            "integrity": "sha512-Vsv7kFaXm+ptHDMZ7izaRsP70GgrW9NBNGswt9OZaVBLlE0SNpDq8eu/VGXyF9r7M0azK3Wy7OlYXsuyYLFzHg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "^1.1.1",
                "@radix-ui/react-dialog": "^1.1.6",
                "@radix-ui/react-id": "^1.1.0",
                "@radix-ui/react-primitive": "^2.0.2"
            },
            "peerDependencies": {
                "react": "^18 || ^19 || ^19.0.0-rc",
                "react-dom": "^18 || ^19 || ^19.0.0-rc"
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/primitive": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz",
            "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==",
            "license": "MIT"
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-compose-refs": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz",
            "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-context": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz",
            "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-dialog": {
            "version": "1.1.14",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.14.tgz",
            "integrity": "sha512-+CpweKjqpzTmwRwcYECQcNYbI8V9VSQt0SNFKeEBLgfucbsLssU6Ppq7wUdNXEGb573bMjFhVjKVll8rmV6zMw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-context": "1.1.2",
                "@radix-ui/react-dismissable-layer": "1.1.10",
                "@radix-ui/react-focus-guards": "1.1.2",
                "@radix-ui/react-focus-scope": "1.1.7",
                "@radix-ui/react-id": "1.1.1",
                "@radix-ui/react-portal": "1.1.9",
                "@radix-ui/react-presence": "1.1.4",
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-slot": "1.2.3",
                "@radix-ui/react-use-controllable-state": "1.2.2",
                "aria-hidden": "^1.2.4",
                "react-remove-scroll": "^2.6.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-dismissable-layer": {
            "version": "1.1.10",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.10.tgz",
            "integrity": "sha512-IM1zzRV4W3HtVgftdQiiOmA0AdJlCtMLe00FXaHwgt3rAnNsIyDqshvkIW3hj/iu5hu8ERP7KIYki6NkqDxAwQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/primitive": "1.1.2",
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-use-callback-ref": "1.1.1",
                "@radix-ui/react-use-escape-keydown": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-focus-guards": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.2.tgz",
            "integrity": "sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-focus-scope": {
            "version": "1.1.7",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.7.tgz",
            "integrity": "sha512-t2ODlkXBQyn7jkl6TNaw/MtVEVvIGelJDCG41Okq/KwUsJBwQ4XVZsHAVUkK4mBv3ewiAS3PGuUWuY2BoK4ZUw==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-use-callback-ref": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-id": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz",
            "integrity": "sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-portal": {
            "version": "1.1.9",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.9.tgz",
            "integrity": "sha512-bpIxvq03if6UNwXZ+HTK71JLh4APvnXntDc6XOX8UVq4XQOVl7lwok0AvIl+b8zgCw3fSaVTZMpAPPagXbKmHQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-primitive": "2.1.3",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-presence": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz",
            "integrity": "sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-primitive": {
            "version": "2.1.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz",
            "integrity": "sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-slot": "1.2.3"
            },
            "peerDependencies": {
                "@types/react": "*",
                "@types/react-dom": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                },
                "@types/react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-slot": {
            "version": "1.2.3",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz",
            "integrity": "sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-compose-refs": "1.1.2"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-use-callback-ref": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz",
            "integrity": "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-use-controllable-state": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz",
            "integrity": "sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-effect-event": "0.0.2",
                "@radix-ui/react-use-layout-effect": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-use-escape-keydown": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz",
            "integrity": "sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==",
            "license": "MIT",
            "dependencies": {
                "@radix-ui/react-use-callback-ref": "1.1.1"
            },
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/cmdk/node_modules/@radix-ui/react-use-layout-effect": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz",
            "integrity": "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==",
            "license": "MIT",
            "peerDependencies": {
                "@types/react": "*",
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"
            },
            "peerDependenciesMeta": {
                "@types/react": {
                    "optional": true
                }
            }
        },
        "node_modules/color": {
            "version": "4.2.3",
            "resolved": "https://registry.npmjs.org/color/-/color-4.2.3.tgz",
            "integrity": "sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==",
            "license": "MIT",
            "optional": true,
            "dependencies": {
                "color-convert": "^2.0.1",
                "color-string": "^1.9.0"
            },
            "engines": {
                "node": ">=12.5.0"
            }
        },
        "node_modules/color-convert": {
            "version": "2.0.1",
            "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz",
            "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==",
            "devOptional": true,
            "license": "MIT",
            "dependencies": {
                "color-name": "~1.1.4"
            },
            "engines": {
                "node": ">=7.0.0"
            }
        },
        "node_modules/color-name": {
            "version": "1.1.4",
            "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz",
            "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==",
            "devOptional": true,
            "license": "MIT"
        },
        "node_modules/color-string": {
            "version": "1.9.1",
            "resolved": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz",
            "integrity": "sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==",
            "license": "MIT",
            "optional": true,
            "dependencies": {
                "color-name": "^1.0.0",
                "simple-swizzle": "^0.2.2"
            }
        },
        "node_modules/commander": {
            "version": "4.1.1",
            "resolved": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz",
            "integrity": "sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 6"
            }
        },
        "node_modules/cookie": {
            "version": "1.0.2",
            "resolved": "https://registry.npmjs.org/cookie/-/cookie-1.0.2.tgz",
            "integrity": "sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==",
            "license": "MIT",
            "engines": {
                "node": ">=18"
            }
        },
        "node_modules/cross-spawn": {
            "version": "7.0.6",
            "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz",
            "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "path-key": "^3.1.0",
                "shebang-command": "^2.0.0",
                "which": "^2.0.1"
            },
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/cssesc": {
            "version": "3.0.0",
            "resolved": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz",
            "integrity": "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==",
            "dev": true,
            "license": "MIT",
            "bin": {
                "cssesc": "bin/cssesc"
            },
            "engines": {
                "node": ">=4"
            }
        },
        "node_modules/csstype": {
            "version": "3.1.3",
            "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz",
            "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==",
            "license": "MIT"
        },
        "node_modules/d3-array": {
            "version": "3.2.4",
            "resolved": "https://registry.npmjs.org/d3-array/-/d3-array-3.2.4.tgz",
            "integrity": "sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==",
            "license": "ISC",
            "dependencies": {
                "internmap": "1 - 2"
            },
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-color": {
            "version": "3.1.0",
            "resolved": "https://registry.npmjs.org/d3-color/-/d3-color-3.1.0.tgz",
            "integrity": "sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==",
            "license": "ISC",
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-ease": {
            "version": "3.0.1",
            "resolved": "https://registry.npmjs.org/d3-ease/-/d3-ease-3.0.1.tgz",
            "integrity": "sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==",
            "license": "BSD-3-Clause",
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-format": {
            "version": "3.1.0",
            "resolved": "https://registry.npmjs.org/d3-format/-/d3-format-3.1.0.tgz",
            "integrity": "sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==",
            "license": "ISC",
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-interpolate": {
            "version": "3.0.1",
            "resolved": "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-3.0.1.tgz",
            "integrity": "sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==",
            "license": "ISC",
            "dependencies": {
                "d3-color": "1 - 3"
            },
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-path": {
            "version": "3.1.0",
            "resolved": "https://registry.npmjs.org/d3-path/-/d3-path-3.1.0.tgz",
            "integrity": "sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==",
            "license": "ISC",
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-scale": {
            "version": "4.0.2",
            "resolved": "https://registry.npmjs.org/d3-scale/-/d3-scale-4.0.2.tgz",
            "integrity": "sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==",
            "license": "ISC",
            "dependencies": {
                "d3-array": "2.10.0 - 3",
                "d3-format": "1 - 3",
                "d3-interpolate": "1.2.0 - 3",
                "d3-time": "2.1.1 - 3",
                "d3-time-format": "2 - 4"
            },
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-shape": {
            "version": "3.2.0",
            "resolved": "https://registry.npmjs.org/d3-shape/-/d3-shape-3.2.0.tgz",
            "integrity": "sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==",
            "license": "ISC",
            "dependencies": {
                "d3-path": "^3.1.0"
            },
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-time": {
            "version": "3.1.0",
            "resolved": "https://registry.npmjs.org/d3-time/-/d3-time-3.1.0.tgz",
            "integrity": "sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==",
            "license": "ISC",
            "dependencies": {
                "d3-array": "2 - 3"
            },
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-time-format": {
            "version": "4.1.0",
            "resolved": "https://registry.npmjs.org/d3-time-format/-/d3-time-format-4.1.0.tgz",
            "integrity": "sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==",
            "license": "ISC",
            "dependencies": {
                "d3-time": "1 - 3"
            },
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/d3-timer": {
            "version": "3.0.1",
            "resolved": "https://registry.npmjs.org/d3-timer/-/d3-timer-3.0.1.tgz",
            "integrity": "sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==",
            "license": "ISC",
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/date-fns": {
            "version": "4.1.0",
            "resolved": "https://registry.npmjs.org/date-fns/-/date-fns-4.1.0.tgz",
            "integrity": "sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==",
            "version": "3.6.0",
            "resolved": "https://registry.npmjs.org/date-fns/-/date-fns-3.6.0.tgz",
            "integrity": "sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==",
            "license": "MIT",
            "funding": {
                "type": "github",
                "url": "https://github.com/sponsors/kossnocorp"
            }
        },
        "node_modules/decimal.js-light": {
            "version": "2.5.1",
            "resolved": "https://registry.npmjs.org/decimal.js-light/-/decimal.js-light-2.5.1.tgz",
            "integrity": "sha512-qIMFpTMZmny+MMIitAB6D7iVPEorVw6YQRWkvarTkT4tBeSLLiHzcwj6q0MmYSFCiVpiqPJTJEYIrpcPzVEIvg==",
            "license": "MIT"
        },
        "node_modules/detect-libc": {
            "version": "2.0.4",
            "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz",
            "integrity": "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==",
            "license": "Apache-2.0",
            "optional": true,
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/detect-node-es": {
            "version": "1.1.0",
            "resolved": "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz",
            "integrity": "sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==",
            "license": "MIT"
        },
        "node_modules/didyoumean": {
            "version": "1.2.2",
            "resolved": "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz",
            "integrity": "sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==",
            "dev": true,
            "license": "Apache-2.0"
        },
        "node_modules/dlv": {
            "version": "1.1.3",
            "resolved": "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz",
            "integrity": "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/dom-helpers": {
            "version": "5.2.1",
            "resolved": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz",
            "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==",
            "license": "MIT",
            "dependencies": {
                "@babel/runtime": "^7.8.7",
                "csstype": "^3.0.2"
            }
        },
        "node_modules/eastasianwidth": {
            "version": "0.2.0",
            "resolved": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz",
            "integrity": "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/electron-to-chromium": {
            "version": "1.5.171",
            "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.171.tgz",
            "integrity": "sha512-scWpzXEJEMrGJa4Y6m/tVotb0WuvNmasv3wWVzUAeCgKU0ToFOhUW6Z+xWnRQANMYGxN4ngJXIThgBJOqzVPCQ==",
            "license": "ISC"
        },
        "node_modules/embla-carousel": {
            "version": "8.5.1",
            "resolved": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.5.1.tgz",
            "integrity": "sha512-JUb5+FOHobSiWQ2EJNaueCNT/cQU9L6XWBbWmorWPQT9bkbk+fhsuLr8wWrzXKagO3oWszBO7MSx+GfaRk4E6A==",
            "license": "MIT"
        },
        "node_modules/embla-carousel-react": {
            "version": "8.5.1",
            "resolved": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.5.1.tgz",
            "integrity": "sha512-z9Y0K84BJvhChXgqn2CFYbfEi6AwEr+FFVVKm/MqbTQ2zIzO1VQri6w67LcfpVF0AjbhwVMywDZqY4alYkjW5w==",
            "license": "MIT",
            "dependencies": {
                "embla-carousel": "8.5.1",
                "embla-carousel-reactive-utils": "8.5.1"
            },
            "peerDependencies": {
                "react": "^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
            }
        },
        "node_modules/embla-carousel-reactive-utils": {
            "version": "8.5.1",
            "resolved": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.5.1.tgz",
            "integrity": "sha512-n7VSoGIiiDIc4MfXF3ZRTO59KDp820QDuyBDGlt5/65+lumPHxX2JLz0EZ23hZ4eg4vZGUXwMkYv02fw2JVo/A==",
            "license": "MIT",
            "peerDependencies": {
                "embla-carousel": "8.5.1"
            }
        },
        "node_modules/emoji-regex": {
            "version": "9.2.2",
            "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz",
            "integrity": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/escalade": {
            "version": "3.2.0",
            "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz",
            "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==",
            "license": "MIT",
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/eventemitter3": {
            "version": "4.0.7",
            "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz",
            "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==",
            "license": "MIT"
        },
        "node_modules/fast-equals": {
            "version": "5.2.2",
            "resolved": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.2.2.tgz",
            "integrity": "sha512-V7/RktU11J3I36Nwq2JnZEM7tNm17eBJz+u25qdxBZeCKiX6BkVSZQjwWIr+IobgnZy+ag73tTZgZi7tr0LrBw==",
            "license": "MIT",
            "engines": {
                "node": ">=6.0.0"
            }
        },
        "node_modules/fast-glob": {
            "version": "3.3.3",
            "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz",
            "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "@nodelib/fs.stat": "^2.0.2",
                "@nodelib/fs.walk": "^1.2.3",
                "glob-parent": "^5.1.2",
                "merge2": "^1.3.0",
                "micromatch": "^4.0.8"
            },
            "engines": {
                "node": ">=8.6.0"
            }
        },
        "node_modules/fast-glob/node_modules/glob-parent": {
            "version": "5.1.2",
            "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",
            "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "is-glob": "^4.0.1"
            },
            "engines": {
                "node": ">= 6"
            }
        },
        "node_modules/fastq": {
            "version": "1.19.1",
            "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz",
            "integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "reusify": "^1.0.4"
            }
        },
        "node_modules/fill-range": {
            "version": "7.1.1",
            "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz",
            "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "to-regex-range": "^5.0.1"
            },
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/foreground-child": {
            "version": "3.3.1",
            "resolved": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz",
            "integrity": "sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "cross-spawn": "^7.0.6",
                "signal-exit": "^4.0.1"
            },
            "engines": {
                "node": ">=14"
            },
            "funding": {
                "url": "https://github.com/sponsors/isaacs"
            }
        },
        "node_modules/fraction.js": {
            "version": "4.3.7",
            "resolved": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz",
            "integrity": "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==",
            "license": "MIT",
            "engines": {
                "node": "*"
            },
            "funding": {
                "type": "patreon",
                "url": "https://github.com/sponsors/rawify"
            }
        },
        "node_modules/framer-motion": {
            "version": "12.18.1",
            "resolved": "https://registry.npmjs.org/framer-motion/-/framer-motion-12.18.1.tgz",
            "integrity": "sha512-6o4EDuRPLk4LSZ1kRnnEOurbQ86MklVk+Y1rFBUKiF+d2pCdvMjWVu0ZkyMVCTwl5UyTH2n/zJEJx+jvTYuxow==",
            "license": "MIT",
            "dependencies": {
                "motion-dom": "^12.18.1",
                "motion-utils": "^12.18.1",
                "tslib": "^2.4.0"
            },
            "peerDependencies": {
                "@emotion/is-prop-valid": "*",
                "react": "^18.0.0 || ^19.0.0",
                "react-dom": "^18.0.0 || ^19.0.0"
            },
            "peerDependenciesMeta": {
                "@emotion/is-prop-valid": {
                    "optional": true
                },
                "react": {
                    "optional": true
                },
                "react-dom": {
                    "optional": true
                }
            }
        },
        "node_modules/fsevents": {
            "version": "2.3.3",
            "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz",
            "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==",
            "dev": true,
            "hasInstallScript": true,
            "license": "MIT",
            "optional": true,
            "os": [
                "darwin"
            ],
            "engines": {
                "node": "^8.16.0 || ^10.6.0 || >=11.0.0"
            }
        },
        "node_modules/function-bind": {
            "version": "1.1.2",
            "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz",
            "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==",
            "dev": true,
            "license": "MIT",
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/get-nonce": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz",
            "integrity": "sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==",
            "license": "MIT",
            "engines": {
                "node": ">=6"
            }
        },
        "node_modules/glob": {
            "version": "10.4.5",
            "resolved": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz",
            "integrity": "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "foreground-child": "^3.1.0",
                "jackspeak": "^3.1.2",
                "minimatch": "^9.0.4",
                "minipass": "^7.1.2",
                "package-json-from-dist": "^1.0.0",
                "path-scurry": "^1.11.1"
            },
            "bin": {
                "glob": "dist/esm/bin.mjs"
            },
            "funding": {
                "url": "https://github.com/sponsors/isaacs"
            }
        },
        "node_modules/glob-parent": {
            "version": "6.0.2",
            "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz",
            "integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "is-glob": "^4.0.3"
            },
            "engines": {
                "node": ">=10.13.0"
            }
        },
        "node_modules/hasown": {
            "version": "2.0.2",
            "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz",
            "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "function-bind": "^1.1.2"
            },
            "engines": {
                "node": ">= 0.4"
            }
        },
        "node_modules/input-otp": {
            "version": "1.4.1",
            "resolved": "https://registry.npmjs.org/input-otp/-/input-otp-1.4.1.tgz",
            "integrity": "sha512-+yvpmKYKHi9jIGngxagY9oWiiblPB7+nEO75F2l2o4vs+6vpPZZmUl4tBNYuTCvQjhvEIbdNeJu70bhfYP2nbw==",
            "license": "MIT",
            "peerDependencies": {
                "react": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc"
            }
        },
        "node_modules/internmap": {
            "version": "2.0.3",
            "resolved": "https://registry.npmjs.org/internmap/-/internmap-2.0.3.tgz",
            "integrity": "sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==",
            "license": "ISC",
            "engines": {
                "node": ">=12"
            }
        },
        "node_modules/is-arrayish": {
            "version": "0.3.2",
            "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz",
            "integrity": "sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==",
            "license": "MIT",
            "optional": true
        },
        "node_modules/is-binary-path": {
            "version": "2.1.0",
            "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz",
            "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "binary-extensions": "^2.0.0"
            },
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/is-core-module": {
            "version": "2.16.1",
            "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz",
            "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "hasown": "^2.0.2"
            },
            "engines": {
                "node": ">= 0.4"
            },
            "funding": {
                "url": "https://github.com/sponsors/ljharb"
            }
        },
        "node_modules/is-extglob": {
            "version": "2.1.1",
            "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz",
            "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/is-fullwidth-code-point": {
            "version": "3.0.0",
            "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz",
            "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/is-glob": {
            "version": "4.0.3",
            "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz",
            "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "is-extglob": "^2.1.1"
            },
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/is-number": {
            "version": "7.0.0",
            "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz",
            "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=0.12.0"
            }
        },
        "node_modules/isexe": {
            "version": "2.0.0",
            "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz",
            "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==",
            "dev": true,
            "license": "ISC"
        },
        "node_modules/jackspeak": {
            "version": "3.4.3",
            "resolved": "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz",
            "integrity": "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==",
            "dev": true,
            "license": "BlueOak-1.0.0",
            "dependencies": {
                "@isaacs/cliui": "^8.0.2"
            },
            "funding": {
                "url": "https://github.com/sponsors/isaacs"
            },
            "optionalDependencies": {
                "@pkgjs/parseargs": "^0.11.0"
            }
        },
        "node_modules/jiti": {
            "version": "1.21.7",
            "resolved": "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz",
            "integrity": "sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==",
            "dev": true,
            "license": "MIT",
            "bin": {
                "jiti": "bin/jiti.js"
            }
        },
        "node_modules/jose": {
            "version": "4.15.9",
            "resolved": "https://registry.npmjs.org/jose/-/jose-4.15.9.tgz",
            "integrity": "sha512-1vUQX+IdDMVPj4k8kOxgUqlcK518yluMuGZwqlr44FS1ppZB/5GWh4rZG89erpOBOJjU/OBsnCVFfapsRz6nEA==",
            "license": "MIT",
            "funding": {
                "url": "https://github.com/sponsors/panva"
            }
        },
        "node_modules/js-tokens": {
            "version": "4.0.0",
            "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz",
            "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==",
            "license": "MIT"
        },
        "node_modules/lilconfig": {
            "version": "3.1.3",
            "resolved": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz",
            "integrity": "sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=14"
            },
            "funding": {
                "url": "https://github.com/sponsors/antonk52"
            }
        },
        "node_modules/lines-and-columns": {
            "version": "1.2.4",
            "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz",
            "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/lodash": {
            "version": "4.17.21",
            "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz",
            "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==",
            "license": "MIT"
        },
        "node_modules/loose-envify": {
            "version": "1.4.0",
            "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz",
            "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==",
            "license": "MIT",
            "dependencies": {
                "js-tokens": "^3.0.0 || ^4.0.0"
            },
            "bin": {
                "loose-envify": "cli.js"
            }
        },
        "node_modules/lru-cache": {
            "version": "10.4.3",
            "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz",
            "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==",
            "dev": true,
            "license": "ISC"
        },
        "node_modules/lucide-react": {
            "version": "0.454.0",
            "resolved": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.454.0.tgz",
            "integrity": "sha512-hw7zMDwykCLnEzgncEEjHeA6+45aeEzRYuKHuyRSOPkhko+J3ySGjGIzu+mmMfDFG1vazHepMaYFYHbTFAZAAQ==",
            "license": "ISC",
            "peerDependencies": {
                "react": "^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0-rc"
            }
        },
        "node_modules/merge2": {
            "version": "1.4.1",
            "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz",
            "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 8"
            }
        },
        "node_modules/micromatch": {
            "version": "4.0.8",
            "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz",
            "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "braces": "^3.0.3",
                "picomatch": "^2.3.1"
            },
            "engines": {
                "node": ">=8.6"
            }
        },
        "node_modules/minimatch": {
            "version": "9.0.5",
            "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz",
            "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==",
            "dev": true,
            "license": "ISC",
            "dependencies": {
                "brace-expansion": "^2.0.1"
            },
            "engines": {
                "node": ">=16 || 14 >=14.17"
            },
            "funding": {
                "url": "https://github.com/sponsors/isaacs"
            }
        },
        "node_modules/minipass": {
            "version": "7.1.2",
            "resolved": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz",
            "integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==",
            "dev": true,
            "license": "ISC",
            "engines": {
                "node": ">=16 || 14 >=14.17"
            }
        },
        "node_modules/motion-dom": {
            "version": "12.18.1",
            "resolved": "https://registry.npmjs.org/motion-dom/-/motion-dom-12.18.1.tgz",
            "integrity": "sha512-dR/4EYT23Snd+eUSLrde63Ws3oXQtJNw/krgautvTfwrN/2cHfCZMdu6CeTxVfRRWREW3Fy1f5vobRDiBb/q+w==",
            "license": "MIT",
            "dependencies": {
                "motion-utils": "^12.18.1"
            }
        },
        "node_modules/motion-utils": {
            "version": "12.18.1",
            "resolved": "https://registry.npmjs.org/motion-utils/-/motion-utils-12.18.1.tgz",
            "integrity": "sha512-az26YDU4WoDP0ueAkUtABLk2BIxe28d8NH1qWT8jPGhPyf44XTdDUh8pDk9OPphaSrR9McgpcJlgwSOIw/sfkA==",
            "license": "MIT"
        },
        "node_modules/mz": {
            "version": "2.7.0",
            "resolved": "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz",
            "integrity": "sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "any-promise": "^1.0.0",
                "object-assign": "^4.0.1",
                "thenify-all": "^1.0.0"
            }
        },
        "node_modules/nanoid": {
            "version": "5.1.5",
            "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-5.1.5.tgz",
            "integrity": "sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==",
            "funding": [
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "bin": {
                "nanoid": "bin/nanoid.js"
            },
            "engines": {
                "node": "^18 || >=20"
            }
        },
        "node_modules/next": {
            "version": "15.2.4",
            "resolved": "https://registry.npmjs.org/next/-/next-15.2.4.tgz",
            "integrity": "sha512-VwL+LAaPSxEkd3lU2xWbgEOtrM8oedmyhBqaVNmgKB+GvZlCy9rgaEc+y2on0wv+l0oSFqLtYD6dcC1eAedUaQ==",
            "license": "MIT",
            "dependencies": {
                "@next/env": "15.2.4",
                "@swc/counter": "0.1.3",
                "@swc/helpers": "0.5.15",
                "busboy": "1.6.0",
                "caniuse-lite": "^1.0.30001579",
                "postcss": "8.4.31",
                "styled-jsx": "5.1.6"
            },
            "bin": {
                "next": "dist/bin/next"
            },
            "engines": {
                "node": "^18.18.0 || ^19.8.0 || >= 20.0.0"
            },
            "optionalDependencies": {
                "@next/swc-darwin-arm64": "15.2.4",
                "@next/swc-darwin-x64": "15.2.4",
                "@next/swc-linux-arm64-gnu": "15.2.4",
                "@next/swc-linux-arm64-musl": "15.2.4",
                "@next/swc-linux-x64-gnu": "15.2.4",
                "@next/swc-linux-x64-musl": "15.2.4",
                "@next/swc-win32-arm64-msvc": "15.2.4",
                "@next/swc-win32-x64-msvc": "15.2.4",
                "sharp": "^0.33.5"
            },
            "peerDependencies": {
                "@opentelemetry/api": "^1.1.0",
                "@playwright/test": "^1.41.2",
                "babel-plugin-react-compiler": "*",
                "react": "^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0",
                "react-dom": "^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0",
                "sass": "^1.3.0"
            },
            "peerDependenciesMeta": {
                "@opentelemetry/api": {
                    "optional": true
                },
                "@playwright/test": {
                    "optional": true
                },
                "babel-plugin-react-compiler": {
                    "optional": true
                },
                "sass": {
                    "optional": true
                }
            }
        },
        "node_modules/next-themes": {
            "version": "0.4.6",
            "resolved": "https://registry.npmjs.org/next-themes/-/next-themes-0.4.6.tgz",
            "integrity": "sha512-pZvgD5L0IEvX5/9GWyHMf3m8BKiVQwsCMHfoFosXtXBMnaS0ZnIJ9ST4b4NqLVKDEm8QBxoNNGNaBv2JNF6XNA==",
            "license": "MIT",
            "peerDependencies": {
                "react": "^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc",
                "react-dom": "^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc"
            }
        },
        "node_modules/next/node_modules/nanoid": {
            "version": "3.3.11",
            "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz",
            "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==",
            "funding": [
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "bin": {
                "nanoid": "bin/nanoid.cjs"
            },
            "engines": {
                "node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"
            }
        },
        "node_modules/next/node_modules/postcss": {
            "version": "8.4.31",
            "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.4.31.tgz",
            "integrity": "sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==",
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/postcss/"
                },
                {
                    "type": "tidelift",
                    "url": "https://tidelift.com/funding/github/npm/postcss"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "nanoid": "^3.3.6",
                "picocolors": "^1.0.0",
                "source-map-js": "^1.0.2"
            },
            "engines": {
                "node": "^10 || ^12 || >=14"
            }
        },
        "node_modules/node-releases": {
            "version": "2.0.19",
            "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz",
            "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==",
            "license": "MIT"
        },
        "node_modules/normalize-path": {
            "version": "3.0.0",
            "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz",
            "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/normalize-range": {
            "version": "0.1.2",
            "resolved": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz",
            "integrity": "sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==",
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/object-assign": {
            "version": "4.1.1",
            "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz",
            "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==",
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/object-hash": {
            "version": "3.0.0",
            "resolved": "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz",
            "integrity": "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 6"
            }
        },
        "node_modules/package-json-from-dist": {
            "version": "1.0.1",
            "resolved": "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz",
            "integrity": "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==",
            "dev": true,
            "license": "BlueOak-1.0.0"
        },
        "node_modules/path-key": {
            "version": "3.1.1",
            "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz",
            "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=8"
            }
        },
        "node_modules/path-parse": {
            "version": "1.0.7",
            "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz",
            "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==",
            "dev": true,
            "license": "MIT"
        },
        "node_modules/path-scurry": {
            "version": "1.11.1",
            "resolved": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz",
            "integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==",
            "dev": true,
            "license": "BlueOak-1.0.0",
            "dependencies": {
                "lru-cache": "^10.2.0",
                "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"
            },
            "engines": {
                "node": ">=16 || 14 >=14.18"
            },
            "funding": {
                "url": "https://github.com/sponsors/isaacs"
            }
        },
        "node_modules/picocolors": {
            "version": "1.1.1",
            "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz",
            "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==",
            "license": "ISC"
        },
        "node_modules/picomatch": {
            "version": "2.3.1",
            "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz",
            "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=8.6"
            },
            "funding": {
                "url": "https://github.com/sponsors/jonschlinkert"
            }
        },
        "node_modules/pify": {
            "version": "2.3.0",
            "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz",
            "integrity": "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/pirates": {
            "version": "4.0.7",
            "resolved": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz",
            "integrity": "sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==",
            "dev": true,
            "license": "MIT",
            "engines": {
                "node": ">= 6"
            }
        },
        "node_modules/postcss": {
            "version": "8.5.6",
            "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz",
            "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==",
            "dev": true,
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/postcss/"
                },
                {
                    "type": "tidelift",
                    "url": "https://tidelift.com/funding/github/npm/postcss"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "nanoid": "^3.3.11",
                "picocolors": "^1.1.1",
                "source-map-js": "^1.2.1"
            },
            "engines": {
                "node": "^10 || ^12 || >=14"
            }
        },
        "node_modules/postcss-import": {
            "version": "15.1.0",
            "resolved": "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz",
            "integrity": "sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "postcss-value-parser": "^4.0.0",
                "read-cache": "^1.0.0",
                "resolve": "^1.1.7"
            },
            "engines": {
                "node": ">=14.0.0"
            },
            "peerDependencies": {
                "postcss": "^8.0.0"
            }
        },
        "node_modules/postcss-js": {
            "version": "4.0.1",
            "resolved": "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz",
            "integrity": "sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "camelcase-css": "^2.0.1"
            },
            "engines": {
                "node": "^12 || ^14 || >= 16"
            },
            "funding": {
                "type": "opencollective",
                "url": "https://opencollective.com/postcss/"
            },
            "peerDependencies": {
                "postcss": "^8.4.21"
            }
        },
        "node_modules/postcss-load-config": {
            "version": "4.0.2",
            "resolved": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz",
            "integrity": "sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==",
            "dev": true,
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/postcss/"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "lilconfig": "^3.0.0",
                "yaml": "^2.3.4"
            },
            "engines": {
                "node": ">= 14"
            },
            "peerDependencies": {
                "postcss": ">=8.0.9",
                "ts-node": ">=9.0.0"
            },
            "peerDependenciesMeta": {
                "postcss": {
                    "optional": true
                },
                "ts-node": {
                    "optional": true
                }
            }
        },
        "node_modules/postcss-nested": {
            "version": "6.2.0",
            "resolved": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz",
            "integrity": "sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==",
            "dev": true,
            "funding": [
                {
                    "type": "opencollective",
                    "url": "https://opencollective.com/postcss/"
                },
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "dependencies": {
                "postcss-selector-parser": "^6.1.1"
            },
            "engines": {
                "node": ">=12.0"
            },
            "peerDependencies": {
                "postcss": "^8.2.14"
            }
        },
        "node_modules/postcss-selector-parser": {
            "version": "6.1.2",
            "resolved": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz",
            "integrity": "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==",
            "dev": true,
            "license": "MIT",
            "dependencies": {
                "cssesc": "^3.0.0",
                "util-deprecate": "^1.0.2"
            },
            "engines": {
                "node": ">=4"
            }
        },
        "node_modules/postcss-value-parser": {
            "version": "4.2.0",
            "resolved": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz",
            "integrity": "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==",
            "license": "MIT"
        },
        "node_modules/postcss/node_modules/nanoid": {
            "version": "3.3.11",
            "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz",
            "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==",
            "dev": true,
            "funding": [
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/ai"
                }
            ],
            "license": "MIT",
            "bin": {
                "nanoid": "bin/nanoid.cjs"
            },
            "engines": {
                "node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"
            }
        },
        "node_modules/prop-types": {
            "version": "15.8.1",
            "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz",
            "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==",
            "license": "MIT",
            "dependencies": {
                "loose-envify": "^1.4.0",
                "object-assign": "^4.1.1",
                "react-is": "^16.13.1"
            }
        },
        "node_modules/prop-types/node_modules/react-is": {
            "version": "16.13.1",
            "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz",
            "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==",
            "license": "MIT"
        },
        "node_modules/queue-microtask": {
            "version": "1.2.3",
            "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz",
            "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==",
            "dev": true,
            "funding": [
                {
                    "type": "github",
                    "url": "https://github.com/sponsors/feross"
                },
                {
                    "type": "patreon",
                    "url": "https://www.patreon.com/feross"
                },
                {
                    "type": "consulting",
                    "url": "https://feross.org/support"
                }
            ],
            "license": "MIT"
        },
        "node_modules/react": {
            "version": "19.1.0",
            "resolved": "https://registry.npmjs.org/react/-/react-19.1.0.tgz",
            "integrity": "sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==",
            "license": "MIT",
            "version": "18.3.1",
            "resolved": "https://registry.npmjs.org/react/-/react-18.3.1.tgz",
            "integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==",
            "license": "MIT",
            "dependencies": {
                "loose-envify": "^1.1.0"
            },
            "engines": {
                "node": ">=0.10.0"
            }
        },
        "node_modules/react-chartjs-2": {
            "version": "5.3.0",
            "resolved": "https://registry.npmjs.org/react-chartjs-2/-/react-chartjs-2-5.3.0.tgz",
            "integrity": "sha512-UfZZFnDsERI3c3CZGxzvNJd02SHjaSJ8kgW1djn65H1KK8rehwTjyrRKOG3VTMG8wtHZ5rgAO5oTHtHi9GCCmw==",
            "license": "MIT",
            "peerDependencies": {
                "chart.js": "^4.1.1",
                "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
            }
        },
        "node_modules/react-day-picker": {
            "version": "8.10.1",
            "resolved": "https://registry.npmjs.org/react-day-picker/-/react-day-picker-8.10.1.tgz",
            "integrity": "sha512-TMx7fNbhLk15eqcMt+7Z7S2KF7mfTId/XJDjKE8f+IUcFn0l08/kI4FiYTL/0yuOLmEcbR4Fwe3GJf/NiiMnPA==",
            "license": "MIT",
            "funding": {
                "type": "individual",
                "url": "https://github.com/sponsors/gpbl"
            },
            "peerDependencies": {
                "date-fns": "^2.28.0 || ^3.0.0",
                "react": "^16.8.0 || ^17.0.0 || ^18.0.0"
            }
        },
        "node_modules/react-dom": {
            "version": "19.1.0",
            "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-19.1.0.tgz",
            "integrity": "sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==",
            "license": "MIT",
            "dependencies": {
                "scheduler": "^0.26.0"
            },
            "peerDependencies": {
                "react": "^19.1.0",
                "version": "18.3.1",
                "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz",
                "integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==",
                "license": "MIT",
                "dependencies": {
                    "loose-envify": "^1.1.0",
                    "scheduler": "^0.23.2"
                },
                "peerDependencies": {
                    "react": "^18.3.1"
                }
            },
            "node_modules/react-hook-form": {
                "version": "7.58.1",
                "resolved": "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.58.1.tgz",
                "integrity": "sha512-Lml/KZYEEFfPhUVgE0RdCVpnC4yhW+PndRhbiTtdvSlQTL8IfVR+iQkBjLIvmmc6+GGoVeM11z37ktKFPAb0FA==",
                "license": "MIT",
                "engines": {
                    "node": ">=18.0.0"
                },
                "funding": {
                    "type": "opencollective",
                    "url": "https://opencollective.com/react-hook-form"
                },
                "peerDependencies": {
                    "react": "^16.8.0 || ^17 || ^18 || ^19"
                }
            },
            "node_modules/react-is": {
                "version": "18.3.1",
                "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz",
                "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==",
                "license": "MIT"
            },
            "node_modules/react-remove-scroll": {
                "version": "2.7.1",
                "resolved": "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.7.1.tgz",
                "integrity": "sha512-HpMh8+oahmIdOuS5aFKKY6Pyog+FNaZV/XyJOq7b4YFwsFHe5yYfdbIalI4k3vU2nSDql7YskmUseHsRrJqIPA==",
                "license": "MIT",
                "dependencies": {
                    "react-remove-scroll-bar": "^2.3.7",
                    "react-style-singleton": "^2.2.3",
                    "tslib": "^2.1.0",
                    "use-callback-ref": "^1.3.3",
                    "use-sidecar": "^1.1.3"
                },
                "engines": {
                    "node": ">=10"
                },
                "peerDependencies": {
                    "@types/react": "*",
                    "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
                },
                "peerDependenciesMeta": {
                    "@types/react": {
                        "optional": true
                    }
                }
            },
            "node_modules/react-remove-scroll-bar": {
                "version": "2.3.8",
                "resolved": "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz",
                "integrity": "sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==",
                "license": "MIT",
                "dependencies": {
                    "react-style-singleton": "^2.2.2",
                    "tslib": "^2.0.0"
                },
                "engines": {
                    "node": ">=10"
                },
                "peerDependencies": {
                    "@types/react": "*",
                    "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
                },
                "peerDependenciesMeta": {
                    "@types/react": {
                        "optional": true
                    }
                }
            },
            "node_modules/react-resizable-panels": {
                "version": "2.1.9",
                "resolved": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.9.tgz",
                "integrity": "sha512-z77+X08YDIrgAes4jl8xhnUu1LNIRp4+E7cv4xHmLOxxUPO/ML7PSrE813b90vj7xvQ1lcf7g2uA9GeMZonjhQ==",
                "license": "MIT",
                "peerDependencies": {
                    "react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc",
                    "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
                }
            },
            "node_modules/react-smooth": {
                "version": "4.0.4",
                "resolved": "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.4.tgz",
                "integrity": "sha512-gnGKTpYwqL0Iii09gHobNolvX4Kiq4PKx6eWBCYYix+8cdw+cGo3do906l1NBPKkSWx1DghC1dlWG9L2uGd61Q==",
                "license": "MIT",
                "dependencies": {
                    "fast-equals": "^5.0.1",
                    "prop-types": "^15.8.1",
                    "react-transition-group": "^4.4.5"
                },
                "peerDependencies": {
                    "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0",
                    "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
                }
            },
            "node_modules/react-style-singleton": {
                "version": "2.2.3",
                "resolved": "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz",
                "integrity": "sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==",
                "license": "MIT",
                "dependencies": {
                    "get-nonce": "^1.0.0",
                    "tslib": "^2.0.0"
                },
                "engines": {
                    "node": ">=10"
                },
                "peerDependencies": {
                    "@types/react": "*",
                    "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
                },
                "peerDependenciesMeta": {
                    "@types/react": {
                        "optional": true
                    }
                }
            },
            "node_modules/react-transition-group": {
                "version": "4.4.5",
                "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz",
                "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==",
                "license": "BSD-3-Clause",
                "dependencies": {
                    "@babel/runtime": "^7.5.5",
                    "dom-helpers": "^5.0.1",
                    "loose-envify": "^1.4.0",
                    "prop-types": "^15.6.2"
                },
                "peerDependencies": {
                    "react": ">=16.6.0",
                    "react-dom": ">=16.6.0"
                }
            },
            "node_modules/read-cache": {
                "version": "1.0.0",
                "resolved": "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz",
                "integrity": "sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "pify": "^2.3.0"
                }
            },
            "node_modules/readdirp": {
                "version": "3.6.0",
                "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz",
                "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "picomatch": "^2.2.1"
                },
                "engines": {
                    "node": ">=8.10.0"
                }
            },
            "node_modules/recharts": {
                "version": "2.15.4",
                "resolved": "https://registry.npmjs.org/recharts/-/recharts-2.15.4.tgz",
                "integrity": "sha512-UT/q6fwS3c1dHbXv2uFgYJ9BMFHu3fwnd7AYZaEQhXuYQ4hgsxLvsUXzGdKeZrW5xopzDCvuA2N41WJ88I7zIw==",
                "license": "MIT",
                "dependencies": {
                    "clsx": "^2.0.0",
                    "eventemitter3": "^4.0.1",
                    "lodash": "^4.17.21",
                    "react-is": "^18.3.1",
                    "react-smooth": "^4.0.4",
                    "recharts-scale": "^0.4.4",
                    "tiny-invariant": "^1.3.1",
                    "victory-vendor": "^36.6.8"
                },
                "engines": {
                    "node": ">=14"
                },
                "peerDependencies": {
                    "react": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0",
                    "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
                }
            },
            "node_modules/recharts-scale": {
                "version": "0.4.5",
                "resolved": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.5.tgz",
                "integrity": "sha512-kivNFO+0OcUNu7jQquLXAxz1FIwZj8nrj+YkOKc5694NbjCvcT6aSZiIzNzd2Kul4o4rTto8QVR9lMNtxD4G1w==",
                "license": "MIT",
                "dependencies": {
                    "decimal.js-light": "^2.4.1"
                }
            },
            "node_modules/resolve": {
                "version": "1.22.10",
                "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz",
                "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "is-core-module": "^2.16.0",
                    "path-parse": "^1.0.7",
                    "supports-preserve-symlinks-flag": "^1.0.0"
                },
                "bin": {
                    "resolve": "bin/resolve"
                },
                "engines": {
                    "node": ">= 0.4"
                },
                "funding": {
                    "url": "https://github.com/sponsors/ljharb"
                }
            },
            "node_modules/reusify": {
                "version": "1.1.0",
                "resolved": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz",
                "integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==",
                "dev": true,
                "license": "MIT",
                "engines": {
                    "iojs": ">=1.0.0",
                    "node": ">=0.10.0"
                }
            },
            "node_modules/run-parallel": {
                "version": "1.2.0",
                "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz",
                "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==",
                "dev": true,
                "funding": [
                    {
                        "type": "github",
                        "url": "https://github.com/sponsors/feross"
                    },
                    {
                        "type": "patreon",
                        "url": "https://www.patreon.com/feross"
                    },
                    {
                        "type": "consulting",
                        "url": "https://feross.org/support"
                    }
                ],
                "license": "MIT",
                "dependencies": {
                    "queue-microtask": "^1.2.2"
                }
            },
            "node_modules/scheduler": {
                "version": "0.26.0",
                "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.26.0.tgz",
                "integrity": "sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==",
                "license": "MIT",
                "version": "0.23.2",
                "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz",
                "integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==",
                "license": "MIT",
                "dependencies": {
                    "loose-envify": "^1.1.0"
                }
            },
            "node_modules/semver": {
                "version": "7.7.2",
                "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz",
                "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==",
                "license": "ISC",
                "optional": true,
                "bin": {
                    "semver": "bin/semver.js"
                },
                "engines": {
                    "node": ">=10"
                }
            },
            "node_modules/set-cookie-parser": {
                "version": "2.7.1",
                "resolved": "https://registry.npmjs.org/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz",
                "integrity": "sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==",
                "license": "MIT"
            },
            "node_modules/sharp": {
                "version": "0.33.5",
                "resolved": "https://registry.npmjs.org/sharp/-/sharp-0.33.5.tgz",
                "integrity": "sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==",
                "hasInstallScript": true,
                "license": "Apache-2.0",
                "optional": true,
                "dependencies": {
                    "color": "^4.2.3",
                    "detect-libc": "^2.0.3",
                    "semver": "^7.6.3"
                },
                "engines": {
                    "node": "^18.17.0 || ^20.3.0 || >=21.0.0"
                },
                "funding": {
                    "url": "https://opencollective.com/libvips"
                },
                "optionalDependencies": {
                    "@img/sharp-darwin-arm64": "0.33.5",
                    "@img/sharp-darwin-x64": "0.33.5",
                    "@img/sharp-libvips-darwin-arm64": "1.0.4",
                    "@img/sharp-libvips-darwin-x64": "1.0.4",
                    "@img/sharp-libvips-linux-arm": "1.0.5",
                    "@img/sharp-libvips-linux-arm64": "1.0.4",
                    "@img/sharp-libvips-linux-s390x": "1.0.4",
                    "@img/sharp-libvips-linux-x64": "1.0.4",
                    "@img/sharp-libvips-linuxmusl-arm64": "1.0.4",
                    "@img/sharp-libvips-linuxmusl-x64": "1.0.4",
                    "@img/sharp-linux-arm": "0.33.5",
                    "@img/sharp-linux-arm64": "0.33.5",
                    "@img/sharp-linux-s390x": "0.33.5",
                    "@img/sharp-linux-x64": "0.33.5",
                    "@img/sharp-linuxmusl-arm64": "0.33.5",
                    "@img/sharp-linuxmusl-x64": "0.33.5",
                    "@img/sharp-wasm32": "0.33.5",
                    "@img/sharp-win32-ia32": "0.33.5",
                    "@img/sharp-win32-x64": "0.33.5"
                }
            },
            "node_modules/shebang-command": {
                "version": "2.0.0",
                "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz",
                "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "shebang-regex": "^3.0.0"
                },
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/shebang-regex": {
                "version": "3.0.0",
                "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz",
                "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==",
                "dev": true,
                "license": "MIT",
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/signal-exit": {
                "version": "4.1.0",
                "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz",
                "integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==",
                "dev": true,
                "license": "ISC",
                "engines": {
                    "node": ">=14"
                },
                "funding": {
                    "url": "https://github.com/sponsors/isaacs"
                }
            },
            "node_modules/simple-swizzle": {
                "version": "0.2.2",
                "resolved": "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz",
                "integrity": "sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==",
                "license": "MIT",
                "optional": true,
                "dependencies": {
                    "is-arrayish": "^0.3.1"
                }
            },
            "node_modules/sonner": {
                "version": "1.7.4",
                "resolved": "https://registry.npmjs.org/sonner/-/sonner-1.7.4.tgz",
                "integrity": "sha512-DIS8z4PfJRbIyfVFDVnK9rO3eYDtse4Omcm6bt0oEr5/jtLgysmjuBl1frJ9E/EQZrFmKx2A8m/s5s9CRXIzhw==",
                "license": "MIT",
                "peerDependencies": {
                    "react": "^18.0.0 || ^19.0.0 || ^19.0.0-rc",
                    "react-dom": "^18.0.0 || ^19.0.0 || ^19.0.0-rc"
                }
            },
            "node_modules/source-map-js": {
                "version": "1.2.1",
                "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz",
                "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==",
                "license": "BSD-3-Clause",
                "engines": {
                    "node": ">=0.10.0"
                }
            },
            "node_modules/streamsearch": {
                "version": "1.1.0",
                "resolved": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz",
                "integrity": "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==",
                "engines": {
                    "node": ">=10.0.0"
                }
            },
            "node_modules/string-width": {
                "version": "5.1.2",
                "resolved": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz",
                "integrity": "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "eastasianwidth": "^0.2.0",
                    "emoji-regex": "^9.2.2",
                    "strip-ansi": "^7.0.1"
                },
                "engines": {
                    "node": ">=12"
                },
                "funding": {
                    "url": "https://github.com/sponsors/sindresorhus"
                }
            },
            "node_modules/string-width-cjs": {
                "name": "string-width",
                "version": "4.2.3",
                "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",
                "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "emoji-regex": "^8.0.0",
                    "is-fullwidth-code-point": "^3.0.0",
                    "strip-ansi": "^6.0.1"
                },
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/string-width-cjs/node_modules/ansi-regex": {
                "version": "5.0.1",
                "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
                "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
                "dev": true,
                "license": "MIT",
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/string-width-cjs/node_modules/emoji-regex": {
                "version": "8.0.0",
                "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",
                "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
                "dev": true,
                "license": "MIT"
            },
            "node_modules/string-width-cjs/node_modules/strip-ansi": {
                "version": "6.0.1",
                "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
                "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "ansi-regex": "^5.0.1"
                },
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/strip-ansi": {
                "version": "7.1.0",
                "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz",
                "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "ansi-regex": "^6.0.1"
                },
                "engines": {
                    "node": ">=12"
                },
                "funding": {
                    "url": "https://github.com/chalk/strip-ansi?sponsor=1"
                }
            },
            "node_modules/strip-ansi-cjs": {
                "name": "strip-ansi",
                "version": "6.0.1",
                "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
                "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "ansi-regex": "^5.0.1"
                },
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/strip-ansi-cjs/node_modules/ansi-regex": {
                "version": "5.0.1",
                "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
                "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
                "dev": true,
                "license": "MIT",
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/styled-jsx": {
                "version": "5.1.6",
                "resolved": "https://registry.npmjs.org/styled-jsx/-/styled-jsx-5.1.6.tgz",
                "integrity": "sha512-qSVyDTeMotdvQYoHWLNGwRFJHC+i+ZvdBRYosOFgC+Wg1vx4frN2/RG/NA7SYqqvKNLf39P2LSRA2pu6n0XYZA==",
                "license": "MIT",
                "dependencies": {
                    "client-only": "0.0.1"
                },
                "engines": {
                    "node": ">= 12.0.0"
                },
                "peerDependencies": {
                    "react": ">= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0"
                },
                "peerDependenciesMeta": {
                    "@babel/core": {
                        "optional": true
                    },
                    "babel-plugin-macros": {
                        "optional": true
                    }
                }
            },
            "node_modules/sucrase": {
                "version": "3.35.0",
                "resolved": "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz",
                "integrity": "sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "@jridgewell/gen-mapping": "^0.3.2",
                    "commander": "^4.0.0",
                    "glob": "^10.3.10",
                    "lines-and-columns": "^1.1.6",
                    "mz": "^2.7.0",
                    "pirates": "^4.0.1",
                    "ts-interface-checker": "^0.1.9"
                },
                "bin": {
                    "sucrase": "bin/sucrase",
                    "sucrase-node": "bin/sucrase-node"
                },
                "engines": {
                    "node": ">=16 || 14 >=14.17"
                }
            },
            "node_modules/supports-preserve-symlinks-flag": {
                "version": "1.0.0",
                "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz",
                "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==",
                "dev": true,
                "license": "MIT",
                "engines": {
                    "node": ">= 0.4"
                },
                "funding": {
                    "url": "https://github.com/sponsors/ljharb"
                }
            },
            "node_modules/tailwind-merge": {
                "version": "2.6.0",
                "resolved": "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-2.6.0.tgz",
                "integrity": "sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==",
                "license": "MIT",
                "funding": {
                    "type": "github",
                    "url": "https://github.com/sponsors/dcastil"
                }
            },
            "node_modules/tailwindcss": {
                "version": "3.4.17",
                "resolved": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.17.tgz",
                "integrity": "sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "@alloc/quick-lru": "^5.2.0",
                    "arg": "^5.0.2",
                    "chokidar": "^3.6.0",
                    "didyoumean": "^1.2.2",
                    "dlv": "^1.1.3",
                    "fast-glob": "^3.3.2",
                    "glob-parent": "^6.0.2",
                    "is-glob": "^4.0.3",
                    "jiti": "^1.21.6",
                    "lilconfig": "^3.1.3",
                    "micromatch": "^4.0.8",
                    "normalize-path": "^3.0.0",
                    "object-hash": "^3.0.0",
                    "picocolors": "^1.1.1",
                    "postcss": "^8.4.47",
                    "postcss-import": "^15.1.0",
                    "postcss-js": "^4.0.1",
                    "postcss-load-config": "^4.0.2",
                    "postcss-nested": "^6.2.0",
                    "postcss-selector-parser": "^6.1.2",
                    "resolve": "^1.22.8",
                    "sucrase": "^3.35.0"
                },
                "bin": {
                    "tailwind": "lib/cli.js",
                    "tailwindcss": "lib/cli.js"
                },
                "engines": {
                    "node": ">=14.0.0"
                }
            },
            "node_modules/tailwindcss-animate": {
                "version": "1.0.7",
                "resolved": "https://registry.npmjs.org/tailwindcss-animate/-/tailwindcss-animate-1.0.7.tgz",
                "integrity": "sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==",
                "license": "MIT",
                "peerDependencies": {
                    "tailwindcss": ">=3.0.0 || insiders"
                }
            },
            "node_modules/tailwindcss/node_modules/postcss-load-config": {
                "version": "4.0.2",
                "resolved": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz",
                "integrity": "sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==",
                "funding": [
                    {
                        "type": "opencollective",
                        "url": "https://opencollective.com/postcss/"
                    },
                    {
                        "type": "github",
                        "url": "https://github.com/sponsors/ai"
                    }
                ],
                "license": "MIT",
                "dependencies": {
                    "lilconfig": "^3.0.0",
                    "yaml": "^2.3.4"
                },
                "engines": {
                    "node": ">= 14"
                },
                "peerDependencies": {
                    "postcss": ">=8.0.9",
                    "ts-node": ">=9.0.0"
                },
                "peerDependenciesMeta": {
                    "postcss": {
                        "optional": true
                    },
                    "ts-node": {
                        "optional": true
                    }
                }
            },
            "node_modules/thenify": {
                "version": "3.3.1",
                "resolved": "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz",
                "integrity": "sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "any-promise": "^1.0.0"
                }
            },
            "node_modules/thenify-all": {
                "version": "1.6.0",
                "resolved": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz",
                "integrity": "sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "thenify": ">= 3.1.0 < 4"
                },
                "engines": {
                    "node": ">=0.8"
                }
            },
            "node_modules/tiny-invariant": {
                "version": "1.3.3",
                "resolved": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.3.tgz",
                "integrity": "sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==",
                "license": "MIT"
            },
            "node_modules/to-regex-range": {
                "version": "5.0.1",
                "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz",
                "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "is-number": "^7.0.0"
                },
                "engines": {
                    "node": ">=8.0"
                }
            },
            "node_modules/tr46": {
                "version": "0.0.3",
                "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz",
                "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==",
                "license": "MIT"
            },
            "node_modules/ts-interface-checker": {
                "version": "0.1.13",
                "resolved": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz",
                "integrity": "sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==",
                "dev": true,
                "license": "Apache-2.0"
            },
            "node_modules/tslib": {
                "version": "2.8.1",
                "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz",
                "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==",
                "license": "0BSD"
            },
            "node_modules/typescript": {
                "version": "5.8.3",
                "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz",
                "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==",
                "dev": true,
                "license": "Apache-2.0",
                "bin": {
                    "tsc": "bin/tsc",
                    "tsserver": "bin/tsserver"
                },
                "engines": {
                    "node": ">=14.17"
                }
            },
            "node_modules/undici-types": {
                "version": "6.21.0",
                "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz",
                "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==",
                "license": "MIT"
            },
            "node_modules/update-browserslist-db": {
                "version": "1.1.3",
                "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz",
                "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==",
                "funding": [
                    {
                        "type": "opencollective",
                        "url": "https://opencollective.com/browserslist"
                    },
                    {
                        "type": "tidelift",
                        "url": "https://tidelift.com/funding/github/npm/browserslist"
                    },
                    {
                        "type": "github",
                        "url": "https://github.com/sponsors/ai"
                    }
                ],
                "license": "MIT",
                "dependencies": {
                    "escalade": "^3.2.0",
                    "picocolors": "^1.1.1"
                },
                "bin": {
                    "update-browserslist-db": "cli.js"
                },
                "peerDependencies": {
                    "browserslist": ">= 4.21.0"
                }
            },
            "node_modules/use-callback-ref": {
                "version": "1.3.3",
                "resolved": "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz",
                "integrity": "sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==",
                "license": "MIT",
                "dependencies": {
                    "tslib": "^2.0.0"
                },
                "engines": {
                    "node": ">=10"
                },
                "peerDependencies": {
                    "@types/react": "*",
                    "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
                },
                "peerDependenciesMeta": {
                    "@types/react": {
                        "optional": true
                    }
                }
            },
            "node_modules/use-sidecar": {
                "version": "1.1.3",
                "resolved": "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz",
                "integrity": "sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==",
                "license": "MIT",
                "dependencies": {
                    "detect-node-es": "^1.1.0",
                    "tslib": "^2.0.0"
                },
                "engines": {
                    "node": ">=10"
                },
                "peerDependencies": {
                    "@types/react": "*",
                    "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"
                },
                "peerDependenciesMeta": {
                    "@types/react": {
                        "optional": true
                    }
                }
            },
            "node_modules/util-deprecate": {
                "version": "1.0.2",
                "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz",
                "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==",
                "dev": true,
                "license": "MIT"
            },
            "node_modules/vaul": {
                "version": "0.9.9",
                "resolved": "https://registry.npmjs.org/vaul/-/vaul-0.9.9.tgz",
                "integrity": "sha512-7afKg48srluhZwIkaU+lgGtFCUsYBSGOl8vcc8N/M3YQlZFlynHD15AE+pwrYdc826o7nrIND4lL9Y6b9WWZZQ==",
                "license": "MIT",
                "dependencies": {
                    "@radix-ui/react-dialog": "^1.1.1"
                },
                "peerDependencies": {
                    "react": "^16.8 || ^17.0 || ^18.0",
                    "react-dom": "^16.8 || ^17.0 || ^18.0"
                }
            },
            "node_modules/victory-vendor": {
                "version": "36.9.2",
                "resolved": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.2.tgz",
                "integrity": "sha512-PnpQQMuxlwYdocC8fIJqVXvkeViHYzotI+NJrCuav0ZYFoq912ZHBk3mCeuj+5/VpodOjPe1z0Fk2ihgzlXqjQ==",
                "license": "MIT AND ISC",
                "dependencies": {
                    "@types/d3-array": "^3.0.3",
                    "@types/d3-ease": "^3.0.0",
                    "@types/d3-interpolate": "^3.0.1",
                    "@types/d3-scale": "^4.0.2",
                    "@types/d3-shape": "^3.1.0",
                    "@types/d3-time": "^3.0.0",
                    "@types/d3-timer": "^3.0.0",
                    "d3-array": "^3.1.6",
                    "d3-ease": "^3.0.1",
                    "d3-interpolate": "^3.0.1",
                    "d3-scale": "^4.0.2",
                    "d3-shape": "^3.1.0",
                    "d3-time": "^3.0.0",
                    "d3-timer": "^3.0.1"
                }
            },
            "node_modules/webidl-conversions": {
                "version": "3.0.1",
                "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz",
                "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==",
                "license": "BSD-2-Clause"
            },
            "node_modules/whatwg-url": {
                "version": "5.0.0",
                "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz",
                "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==",
                "license": "MIT",
                "dependencies": {
                    "tr46": "~0.0.3",
                    "webidl-conversions": "^3.0.0"
                }
            },
            "node_modules/which": {
                "version": "2.0.2",
                "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz",
                "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==",
                "dev": true,
                "license": "ISC",
                "dependencies": {
                    "isexe": "^2.0.0"
                },
                "bin": {
                    "node-which": "bin/node-which"
                },
                "engines": {
                    "node": ">= 8"
                }
            },
            "node_modules/wrap-ansi": {
                "version": "8.1.0",
                "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz",
                "integrity": "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "ansi-styles": "^6.1.0",
                    "string-width": "^5.0.1",
                    "strip-ansi": "^7.0.1"
                },
                "engines": {
                    "node": ">=12"
                },
                "funding": {
                    "url": "https://github.com/chalk/wrap-ansi?sponsor=1"
                }
            },
            "node_modules/wrap-ansi-cjs": {
                "name": "wrap-ansi",
                "version": "7.0.0",
                "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz",
                "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "ansi-styles": "^4.0.0",
                    "string-width": "^4.1.0",
                    "strip-ansi": "^6.0.0"
                },
                "engines": {
                    "node": ">=10"
                },
                "funding": {
                    "url": "https://github.com/chalk/wrap-ansi?sponsor=1"
                }
            },
            "node_modules/wrap-ansi-cjs/node_modules/ansi-regex": {
                "version": "5.0.1",
                "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz",
                "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==",
                "dev": true,
                "license": "MIT",
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/wrap-ansi-cjs/node_modules/ansi-styles": {
                "version": "4.3.0",
                "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz",
                "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "color-convert": "^2.0.1"
                },
                "engines": {
                    "node": ">=8"
                },
                "funding": {
                    "url": "https://github.com/chalk/ansi-styles?sponsor=1"
                }
            },
            "node_modules/wrap-ansi-cjs/node_modules/emoji-regex": {
                "version": "8.0.0",
                "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz",
                "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==",
                "dev": true,
                "license": "MIT"
            },
            "node_modules/wrap-ansi-cjs/node_modules/string-width": {
                "version": "4.2.3",
                "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz",
                "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "emoji-regex": "^8.0.0",
                    "is-fullwidth-code-point": "^3.0.0",
                    "strip-ansi": "^6.0.1"
                },
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/wrap-ansi-cjs/node_modules/strip-ansi": {
                "version": "6.0.1",
                "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz",
                "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==",
                "dev": true,
                "license": "MIT",
                "dependencies": {
                    "ansi-regex": "^5.0.1"
                },
                "engines": {
                    "node": ">=8"
                }
            },
            "node_modules/ws": {
                "version": "8.18.2",
                "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz",
                "integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==",
                "license": "MIT",
                "engines": {
                    "node": ">=10.0.0"
                },
                "peerDependencies": {
                    "bufferutil": "^4.0.1",
                    "utf-8-validate": ">=5.0.2"
                },
                "peerDependenciesMeta": {
                    "bufferutil": {
                        "optional": true
                    },
                    "utf-8-validate": {
                        "optional": true
                    }
                }
            },
            "node_modules/yaml": {
                "version": "2.8.0",
                "resolved": "https://registry.npmjs.org/yaml/-/yaml-2.8.0.tgz",
                "integrity": "sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==",
                "dev": true,
                "license": "ISC",
                "bin": {
                    "yaml": "bin.mjs"
                },
                "engines": {
                    "node": ">= 14.6"
                }
            },
            "node_modules/zod": {
                "version": "3.25.67",
                "resolved": "https://registry.npmjs.org/zod/-/zod-3.25.67.tgz",
                "integrity": "sha512-idA2YXwpCdqUSKRCACDE6ItZD9TZzy3OZMtpfLoh6oPR47lipysRrJfjzMqFxQ3uJuUPyUeWe1r9vLH33xO/Qw==",
                "license": "MIT",
                "funding": {
                    "url": "https://github.com/sponsors/colinhacks"
                }
            }
        }
    }
"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Calendar, Clock, FileText, User, AlertCircle, Tag, MessageSquare } from "lucide-react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { MedicalHistory } from "@/types/patient-records"
import { format } from "date-fns"

interface MedicalHistoryDetailProps {
  historyId: string
}

export function MedicalHistoryDetail({ historyId }: MedicalHistoryDetailProps) {
  const [history, setHistory] = useState<MedicalHistory | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient()

  useEffect(() => {
    async function fetchMedicalHistory() {
      try {
        setLoading(true)
        const { data, error } = await supabase
          .from("medical_history")
          .select(`
            *,
            patients!inner(id, full_name)
          `)
          .eq("id", historyId)
          // .single()

        if (error) throw error
        setHistory(data as unknown as MedicalHistory)
      } catch (error: any) {
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    if (historyId) {
      fetchMedicalHistory()
    }
  }, [historyId, supabase])

  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex items-center text-red-500">
            <AlertCircle className="mr-2" />
            <p>Error loading medical history: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!history) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <p className="text-muted-foreground">No medical history found</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-bold">{history.diagnosis}</CardTitle>
            <CardDescription>
              <div className="flex items-center mt-1">
                <Calendar className="h-4 w-4 mr-1" />
                <span className="text-sm">
                  {history.created_at ? format(new Date(history.created_at), "PPP") : "Date not available"}
                </span>
              </div>
            </CardDescription>
          </div>
          <Badge variant={history.status === "active" ? "destructive" : "outline"}>{history.status}</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="details">
          <TabsList className="mb-4">
            <TabsTrigger value="details">Chi tiết</TabsTrigger>
            <TabsTrigger value="treatment">Điều trị</TabsTrigger>
            <TabsTrigger value="notes">Ghi chú</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-2">Thông tin bệnh nhân</h4>
                <div className="bg-muted p-3 rounded-md">
                  <div className="flex items-center mb-2">
                    <User className="h-4 w-4 mr-2 text-primary" />
                    <span className="font-medium">{history.patients?.full_name}</span>
                  </div>
                  <div className="flex items-center">
                    <Tag className="h-4 w-4 mr-2 text-primary" />
                    <span>ID: {history.patient_id}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-2">Thông tin bác sĩ</h4>
                <div className="bg-muted p-3 rounded-md">
                  <div className="flex items-center mb-2">
                    <User className="h-4 w-4 mr-2 text-primary" />
                    <span className="font-medium">{history.medical_staff?.full_name}</span>
                  </div>
                  <div className="flex items-center">
                    <Tag className="h-4 w-4 mr-2 text-primary" />
                    <span>{history.medical_staff?.specialty}</span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Triệu chứng</h4>
              <p className="bg-muted p-3 rounded-md">{history.symptoms || "Không có thông tin"}</p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Chẩn đoán</h4>
              <p className="bg-muted p-3 rounded-md">{history.diagnosis}</p>
            </div>

            {history.test_results && (
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-2">Kết quả xét nghiệm</h4>
                <p className="bg-muted p-3 rounded-md whitespace-pre-line">{history.test_results}</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="treatment" className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Phương pháp điều trị</h4>
              <p className="bg-muted p-3 rounded-md whitespace-pre-line">
                {history.treatment_plan || "Chưa có phương pháp điều trị"}
              </p>
            </div>

            {history.medications && (
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-2">Thuốc</h4>
                <div className="bg-muted p-3 rounded-md">
                  <ul className="list-disc pl-5 space-y-1">
                    {history.medications.split("\n").map((med, idx) => (
                      <li key={idx}>{med}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {history.follow_up && (
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-2">Lịch tái khám</h4>
                <div className="bg-muted p-3 rounded-md flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-primary" />
                  <span>{history.follow_up}</span>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="notes" className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Ghi chú của bác sĩ</h4>
              <p className="bg-muted p-3 rounded-md whitespace-pre-line min-h-[100px]">
                {history.notes || "Không có ghi chú"}
              </p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Ghi chú bổ sung</h4>
              <p className="bg-muted p-3 rounded-md whitespace-pre-line min-h-[100px]">
                {history.additional_notes || "Không có ghi chú bổ sung"}
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <div className="flex items-center text-sm text-muted-foreground">
          <Clock className="h-4 w-4 mr-1" />
          <span>Cập nhật: {history.updated_at ? format(new Date(history.updated_at), "PPP") : "N/A"}</span>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Xuất PDF
          </Button>
          <Button variant="outline" size="sm">
            <MessageSquare className="h-4 w-4 mr-2" />
            Trao đổi với bác sĩ
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}

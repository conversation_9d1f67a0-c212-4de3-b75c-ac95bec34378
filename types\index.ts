export type UserRole = "admin" | "patient" | "medical_staff"
export type UserStatus = "online" | "offline" | "busy"
export type MessageType = "text" | "image" | "file" | "system"
export type ConsultationType = "multidisciplinary" | "individual" | "followup"
export type MeetingStatus = "scheduled" | "in-progress" | "completed" | "cancelled"
export type ArticleStatus = "draft" | "published" | "archived"
export type ArticleCategory = "news" | "blog" | "guide" | "faq"
export type PageStatus = "draft" | "published" | "archived"
export type TestimonialStatus = "pending" | "approved" | "rejected"
export type ContactRequestStatus = "new" | "in-progress" | "resolved"

export interface User {
  id: string
  email: string
  full_name: string
  role: User<PERSON><PERSON>
  phone_number?: string
  date_of_birth?: string
  gender?: string
  address?: string
  avatar_url?: string
  status?: UserStatus
  last_seen_at?: string
  created_at: string
  updated_at: string
}

export interface MedicalStaff {
  id: string
  specialization: string
  license_number: string
  hospital?: string
  department?: string
  years_of_experience?: number
  education?: string
  bio?: string
  is_verified: boolean
  availability?: AvailabilitySchedule[]
  user?: User
}

export interface AvailabilitySchedule {
  id: string
  medical_staff_id: string
  day_of_week: number
  start_time: string
  end_time: string
  is_available: boolean
}

export interface Patient {
  id: string
  medical_record_number?: string
  emergency_contact_name?: string
  emergency_contact_phone?: string
  blood_type?: string
  allergies?: string[]
  current_medications?: string[]
  diagnosis?: string
  diagnosis_date?: string
  cancer_type?: string
  cancer_stage?: string
  treatment_status?: string
  user?: User
}

export interface ConsultationRequest {
  id: string
  patient_id: string
  title: string
  description: string
  cancer_type: string
  consultation_type: ConsultationType
  status: "pending" | "approved" | "rejected" | "completed"
  priority: "low" | "normal" | "high" | "urgent"
  preferred_date?: string
  preferred_time?: string
  created_at: string
  updated_at: string
  patient?: Patient
}

export interface Consultation {
  id: string
  request_id?: string
  title: string
  description?: string
  consultation_type: ConsultationType
  scheduled_date?: string
  duration?: number
  status: MeetingStatus
  meeting_link?: string
  meeting_id?: string
  meeting_password?: string
  recording_url?: string
  created_at: string
  updated_at: string
  participants?: ConsultationParticipant[]
  notes?: ConsultationNote[]
  decisions?: ConsultationDecision[]
  request?: ConsultationRequest
  messages?: Message[]
}

export interface ConsultationParticipant {
  id: string
  consultation_id: string
  user_id: string
  role: string
  is_coordinator: boolean
  joined_at?: string
  left_at?: string
  user?: User
}

export interface ConsultationNote {
  id: string
  consultation_id: string
  author_id: string
  content: string
  is_private: boolean
  created_at: string
  updated_at: string
  author?: User
}

export interface ConsultationDecision {
  id: string
  consultation_id: string
  decision: string
  rationale?: string
  created_by: string
  created_at: string
  updated_at: string
  creator?: User
}

export interface MedicalDocument {
  id: string
  patient_id: string
  uploaded_by: string
  title: string
  description?: string
  file_url: string
  file_type: string
  document_date?: string
  document_type: string
  is_private: boolean
  created_at: string
  uploader?: User
  patient?: Patient
}

export interface Message {
  id: string
  conversation_id: string
  sender_id: string
  content: string
  type: MessageType
  file_url?: string
  is_read: boolean
  created_at: string
  sender?: User
}

export interface Conversation {
  id: string
  title?: string
  is_group: boolean
  consultation_id?: string
  created_at: string
  updated_at: string
  participants: ConversationParticipant[]
  last_message?: Message
}

export interface ConversationParticipant {
  id: string
  conversation_id: string
  user_id: string
  joined_at: string
  left_at?: string
  user?: User
}

export interface Notification {
  id: string
  user_id: string
  title: string
  content: string
  type: string
  is_read: boolean
  action_url?: string
  created_at: string
}

// Các interface mới cho quản lý nội dung
export interface Article {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  featured_image?: string
  author_id?: string
  status: ArticleStatus
  category: ArticleCategory
  tags?: string[]
  published_at?: string
  created_at: string
  updated_at: string
  author?: User
}

export interface Page {
  id: string
  title: string
  slug: string
  content: string
  status: PageStatus
  created_at: string
  updated_at: string
}

export interface Testimonial {
  id: string
  name: string
  role: string
  content: string
  rating: number
  avatar_url?: string
  status: TestimonialStatus
  created_at: string
  updated_at: string
}

export interface ContactRequest {
  id: string
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  status: ContactRequestStatus
  created_at: string
  updated_at: string
}

export interface SystemSettings {
  id: string
  value: Record<string, any>
  updated_at: string
  updated_by?: string
  updater?: User
}

export interface AdminActivity {
  id: string
  user_id: string
  action: string
  entity_type: string
  entity_id: string
  details?: Record<string, any>
  created_at: string
  user?: User
}

export interface PatientInfo {
  name: string
  dateOfBirth: Date | null | undefined
  gender: string
  address: string
  phoneNumber: string
  email: string
  medicalHistory: string[]
  allergies: string[]
  medications: string[]
  labResults: string[]
  vitalSigns: string[]
  documents: string[]
  fullName: string
  

}

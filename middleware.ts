import { createMiddlewareClient } from "@supabase/auth-helpers-nextjs"
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
// import { updateSession } from '@/utils/supabase/middleware'

// C<PERSON><PERSON> trang không yêu cầu xác thực
const publicPages = [
  "/",
  "/about",
  "/knowledge",
  "/resources",
  "/contact",
  "/login",
  "/register",
  "/auth/callback",
  "/shared",
  "/images",
]

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })

  const {
    data: { session },
  } = await supabase.auth.getSession()

  const { pathname } = req.nextUrl

  // Kiểm tra xem đường dẫn hiện tại có phải là trang công khai
  const isPublicPage = publicPages.some((page) => pathname === page || pathname.startsWith(`${page}/`))

  // Cho phép truy cập vào các trang công khai mà không cần đăng nhập
  if (isPublicPage) {
    return res
  }

  // Nếu không có phiên và không phải trang công khai, chuyển hướng đến trang đăng nhập
  if (!session && !isPublicPage) {
    return NextResponse.redirect(new URL("/login", req.url))
  }

  // If user is signed in but trying to access login page, redirect to dashboard
  if (session && (req.nextUrl.pathname === "/login" || req.nextUrl.pathname === "/")) {
    // Let the dashboard layout handle the profile check
    return NextResponse.redirect(new URL("/dashboard", req.url))
  }

  // Check admin access
  if (req.nextUrl.pathname.startsWith("/admin")) {
    if (!session) {
      return NextResponse.redirect(new URL("/login", req.url))
    }

    // Check if user is admin
    const { data: userData } = await supabase.from("users").select("role").eq("id", session.user.id).single()

    if (!userData || userData.role !== "admin") {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }
  }

  // Kiểm tra quyền truy cập cho trang /advisory/request
  if (req.nextUrl.pathname === "/advisory/request") {
    if (!session) {
      return NextResponse.redirect(new URL("/login", req.url))
    }

    // Kiểm tra vai trò người dùng
    const { data: userData } = await supabase.from("users").select("role").eq("id", session.user.id).single()

    // Chỉ cho phép bệnh nhân truy cập trang yêu cầu tư vấn
    if (!userData || userData.role !== "patient") {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }

    // Nếu là bệnh nhân, cho phép truy cập
    return res
  }

  // For all other routes, let the request proceed
  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - files with extensions (e.g. images in /public)
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.).*)",
  ],
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { Activity, Heart, Thermometer, Droplet, Scale, TrendingUp, TrendingDown, AlertCircle } from "lucide-react"
import { format } from "date-fns"

interface HealthMetric {
  id: string
  patient_id: string
  metric_type: string
  value: number
  unit: string
  created_at: string
  notes?: string
}

interface HealthOverviewProps {
  patientId: string
}

export function HealthOverview({ patientId }: HealthOverviewProps) {
  const [metrics, setMetrics] = useState<HealthMetric[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient()

  useEffect(() => {
    async function fetchHealthMetrics() {
      try {
        setLoading(true)
        const { data, error } = await supabase
          .from("health_metrics")
          .select("*")
          .eq("patient_id", patientId)
          .order("created_at", { ascending: false })
          .limit(50)

        if (error) throw error
        setMetrics(data)
      } catch (error: any) {
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    if (patientId) {
      fetchHealthMetrics()
    }
  }, [patientId, supabase])

  const getLatestMetric = (type: string) => {
    return metrics
      .filter((m) => m.metric_type === type)
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0]
  }

  const getMetricTrend = (type: string) => {
    const filteredMetrics = metrics
      .filter((m) => m.metric_type === type)
      .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())

    if (filteredMetrics.length < 2) return "stable"

    const latest = filteredMetrics[filteredMetrics.length - 1]
    const previous = filteredMetrics[filteredMetrics.length - 2]

    const diff = latest.value - previous.value
    const percentChange = (diff / previous.value) * 100

    if (percentChange > 5) return "up"
    if (percentChange < -5) return "down"
    return "stable"
  }

  const getMetricIcon = (type: string) => {
    switch (type) {
      case "blood_pressure":
        return <Activity className="h-5 w-5 text-red-500" />
      case "heart_rate":
        return <Heart className="h-5 w-5 text-red-500" />
      case "temperature":
        return <Thermometer className="h-5 w-5 text-orange-500" />
      case "blood_sugar":
        return <Droplet className="h-5 w-5 text-blue-500" />
      case "weight":
        return <Scale className="h-5 w-5 text-green-500" />
      default:
        return <Activity className="h-5 w-5 text-gray-500" />
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-red-500" />
      case "down":
        return <TrendingDown className="h-4 w-4 text-green-500" />
      default:
        return null
    }
  }

  const getMetricName = (type: string) => {
    switch (type) {
      case "blood_pressure":
        return "Huyết áp"
      case "heart_rate":
        return "Nhịp tim"
      case "temperature":
        return "Nhiệt độ"
      case "blood_sugar":
        return "Đường huyết"
      case "weight":
        return "Cân nặng"
      default:
        return type
    }
  }

  const formatMetricValue = (metric: HealthMetric) => {
    if (metric.metric_type === "blood_pressure") {
      // Assuming blood pressure is stored as "systolic/diastolic"
      return metric.value
    }
    return `${metric.value} ${metric.unit}`
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center text-red-500">
            <AlertCircle className="mr-2" />
            <p>Error loading health metrics: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const metricTypes = ["blood_pressure", "heart_rate", "temperature", "blood_sugar", "weight"]
  const availableMetricTypes = [...new Set(metrics.map((m) => m.metric_type))]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tổng quan sức khỏe</CardTitle>
        <CardDescription>Các chỉ số sức khỏe gần đây nhất</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview">
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Tổng quan</TabsTrigger>
            <TabsTrigger value="history">Lịch sử</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {metricTypes.map((type) => {
                const latestMetric = getLatestMetric(type)
                if (!latestMetric) return null

                const trend = getMetricTrend(type)

                return (
                  <div key={type} className="bg-muted p-4 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center">
                        {getMetricIcon(type)}
                        <h3 className="font-medium ml-2">{getMetricName(type)}</h3>
                      </div>
                      {getTrendIcon(trend)}
                    </div>

                    <div className="flex items-baseline">
                      <span className="text-2xl font-bold">{formatMetricValue(latestMetric)}</span>
                    </div>

                    <div className="mt-2 text-xs text-muted-foreground">
                      Cập nhật: {format(new Date(latestMetric.created_at), "dd/MM/yyyy HH:mm")}
                    </div>
                  </div>
                )
              })}

              {availableMetricTypes.length === 0 && (
                <div className="col-span-full text-center py-6 text-muted-foreground">
                  Chưa có dữ liệu chỉ số sức khỏe
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="history">
            <div className="space-y-4">
              {availableMetricTypes.map((type) => (
                <div key={type} className="border rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    {getMetricIcon(type)}
                    <h3 className="font-medium ml-2">{getMetricName(type)}</h3>
                  </div>

                  <div className="space-y-2">
                    {metrics
                      .filter((m) => m.metric_type === type)
                      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
                      .slice(0, 5)
                      .map((metric) => (
                        <div key={metric.id} className="flex justify-between items-center py-2 border-b last:border-0">
                          <span>{formatMetricValue(metric)}</span>
                          <Badge variant="outline">{format(new Date(metric.created_at), "dd/MM/yyyy HH:mm")}</Badge>
                        </div>
                      ))}
                  </div>
                </div>
              ))}

              {availableMetricTypes.length === 0 && (
                <div className="text-center py-6 text-muted-foreground">Chưa có dữ liệu chỉ số sức khỏe</div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

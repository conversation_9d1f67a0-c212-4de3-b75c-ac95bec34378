"use client"

import { useState, useEffect } from "react"
import { createClient } from "@/lib/supabase/client"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { FileText, User, ClipboardList, Pill, Activity, AlertTriangle, Lock, Shield } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

interface SharedRecordViewProps {
  token: string
}

export default function SharedRecordView({ token }: SharedRecordViewProps) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [shareData, setShareData] = useState<any>(null)
  const [patientData, setPatientData] = useState<any>(null)
  const [medicalHistory, setMedicalHistory] = useState<any[]>([])
  const [documents, setDocuments] = useState<any[]>([])
  const [prescriptions, setPrescriptions] = useState<any[]>([])
  const [symptoms, setSymptoms] = useState<any[]>([])
  const { toast } = useToast()
  const supabase = createClient()

  useEffect(() => {
    fetchSharedRecord()
  }, [token])

  const fetchSharedRecord = async () => {
    try {
      setLoading(true)

      // Verify the share token
      const { data: shareData, error: shareError } = await supabase
        .from("medical_record_shares")
        .select(`
          id,
          patient_id,
          doctor_id,
          expires_at,
          access_level,
          created_at,
          patients(
            id,
            user_id,
            date_of_birth,
            gender,
            blood_type,
            emergency_contact,
            users(
              id,
              full_name,
              email,
              avatar_url,
              phone
            )
          )
        `)
        .eq("token", token)
        .single()

      if (shareError) throw shareError

      if (!shareData) {
        setError("Liên kết chia sẻ không hợp lệ hoặc đã hết hạn")
        return
      }

      // Check if the share has expired
      if (shareData.expires_at && new Date(shareData.expires_at) < new Date()) {
        setError("Liên kết chia sẻ đã hết hạn")
        return
      }

      setShareData(shareData)
      setPatientData(shareData.patients)

      // Fetch medical history
      const { data: historyData, error: historyError } = await supabase
        .from("medical_history")
        .select("*")
        .eq("patient_id", shareData.patient_id)

      if (historyError) throw historyError
      setMedicalHistory(historyData || [])

      // Fetch documents
      const { data: documentsData, error: documentsError } = await supabase
        .from("patient_documents")
        .select("*")
        .eq("patient_id", shareData.patient_id)

      if (documentsError) throw documentsError
      setDocuments(documentsData || [])

      // Fetch prescriptions
      const { data: prescriptionsData, error: prescriptionsError } = await supabase
        .from("prescriptions")
        .select("*")
        .eq("patient_id", shareData.patient_id)

      if (prescriptionsError) throw prescriptionsError
      setPrescriptions(prescriptionsData || [])

      // Fetch symptoms
      const { data: symptomsData, error: symptomsError } = await supabase
        .from("patient_symptoms")
        .select("*")
        .eq("patient_id", shareData.patient_id)

      if (symptomsError) throw symptomsError
      setSymptoms(symptomsData || [])

      // Log access
      await supabase.from("medical_record_access_logs").insert({
        share_id: shareData.id,
        accessed_by: shareData.doctor_id,
        access_type: "view",
      })
    } catch (error) {
      console.error("Error fetching shared record:", error)
      setError("Không thể tải hồ sơ y tế được chia sẻ")
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!patientData) {
    return (
      <Alert>
        <Lock className="h-4 w-4" />
        <AlertTitle>Không tìm thấy hồ sơ</AlertTitle>
        <AlertDescription>Hồ sơ y tế không tồn tại hoặc bạn không có quyền truy cập.</AlertDescription>
      </Alert>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Hồ sơ y tế được chia sẻ</h1>
        <Badge variant="outline" className="flex items-center gap-1">
          <Shield className="h-3 w-3" />
          {shareData.access_level === "read_only"
            ? "Chỉ xem"
            : shareData.access_level === "limited_edit"
              ? "Chỉnh sửa hạn chế"
              : "Truy cập đầy đủ"}
        </Badge>
      </div>

      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Thông tin bảo mật</AlertTitle>
        <AlertDescription>
          Hồ sơ này được chia sẻ với bạn vào {formatDate(shareData.created_at)}.
          {shareData.expires_at && ` Quyền truy cập sẽ hết hạn vào ${formatDate(shareData.expires_at)}.`}
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader className="bg-gray-50">
          <div className="flex items-center gap-4">
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
              <User className="h-6 w-6 text-primary" />
            </div>
            <div>
              <CardTitle>{patientData.users?.full_name}</CardTitle>
              <CardDescription>
                {patientData.gender},{" "}
                {patientData.date_of_birth
                  ? `${new Date().getFullYear() - new Date(patientData.date_of_birth).getFullYear()} tuổi`
                  : "Không có thông tin tuổi"}
                {patientData.blood_type ? `, Nhóm máu ${patientData.blood_type}` : ""}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      <Tabs defaultValue="medical_history">
        <TabsList className="grid grid-cols-4 md:grid-cols-5">
          <TabsTrigger value="medical_history" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="hidden md:inline">Tiền sử bệnh</span>
            <span className="md:hidden">Tiền sử</span>
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <ClipboardList className="h-4 w-4" />
            <span className="hidden md:inline">Tài liệu y tế</span>
            <span className="md:hidden">Tài liệu</span>
          </TabsTrigger>
          <TabsTrigger value="prescriptions" className="flex items-center gap-2">
            <Pill className="h-4 w-4" />
            <span className="hidden md:inline">Đơn thuốc</span>
            <span className="md:hidden">Thuốc</span>
          </TabsTrigger>
          <TabsTrigger value="symptoms" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            <span className="hidden md:inline">Triệu chứng</span>
            <span className="md:hidden">Triệu chứng</span>
          </TabsTrigger>
          <TabsTrigger value="contact" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span className="hidden md:inline">Thông tin liên hệ</span>
            <span className="md:hidden">Liên hệ</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="medical_history" className="mt-6">
          {medicalHistory.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-6">
                  <FileText className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium">Chưa có thông tin tiền sử bệnh</h3>
                  <p className="mt-1 text-sm text-gray-500">Bệnh nhân chưa cập nhật thông tin tiền sử bệnh.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {medicalHistory.map((item) => (
                <Card key={item.id}>
                  <CardHeader>
                    <CardTitle>{item.condition}</CardTitle>
                    <CardDescription>
                      {item.diagnosis_date
                        ? `Chẩn đoán: ${formatDate(item.diagnosis_date)}`
                        : "Không có ngày chẩn đoán"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div>
                        <h4 className="font-medium">Mô tả</h4>
                        <p className="text-sm text-gray-600">{item.description || "Không có mô tả"}</p>
                      </div>
                      <div>
                        <h4 className="font-medium">Điều trị</h4>
                        <p className="text-sm text-gray-600">{item.treatment || "Không có thông tin điều trị"}</p>
                      </div>
                      {item.notes && (
                        <div>
                          <h4 className="font-medium">Ghi chú</h4>
                          <p className="text-sm text-gray-600">{item.notes}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="documents" className="mt-6">
          {documents.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-6">
                  <ClipboardList className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium">Chưa có tài liệu y tế</h3>
                  <p className="mt-1 text-sm text-gray-500">Bệnh nhân chưa tải lên tài liệu y tế nào.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {documents.map((doc) => (
                <Card key={doc.id}>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">{doc.title}</CardTitle>
                    <CardDescription>Tải lên: {formatDate(doc.created_at)}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">{doc.description || "Không có mô tả"}</p>
                    <Button size="sm" variant="outline" asChild>
                      <a href={doc.file_url} target="_blank" rel="noopener noreferrer">
                        Xem tài liệu
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="prescriptions" className="mt-6">
          {prescriptions.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-6">
                  <Pill className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium">Chưa có đơn thuốc</h3>
                  <p className="mt-1 text-sm text-gray-500">Bệnh nhân chưa có đơn thuốc nào.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {prescriptions.map((prescription) => (
                <Card key={prescription.id}>
                  <CardHeader>
                    <CardTitle>{prescription.medication_name}</CardTitle>
                    <CardDescription>Kê đơn: {formatDate(prescription.prescribed_date)}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div>
                        <h4 className="font-medium">Liều lượng</h4>
                        <p className="text-sm text-gray-600">{prescription.dosage}</p>
                      </div>
                      <div>
                        <h4 className="font-medium">Hướng dẫn</h4>
                        <p className="text-sm text-gray-600">{prescription.instructions}</p>
                      </div>
                      <div>
                        <h4 className="font-medium">Thời gian dùng</h4>
                        <p className="text-sm text-gray-600">
                          {formatDate(prescription.start_date)} -
                          {prescription.end_date ? formatDate(prescription.end_date) : "Không xác định"}
                        </p>
                      </div>
                      {prescription.notes && (
                        <div>
                          <h4 className="font-medium">Ghi chú</h4>
                          <p className="text-sm text-gray-600">{prescription.notes}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="symptoms" className="mt-6">
          {symptoms.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-6">
                  <Activity className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium">Chưa có triệu chứng</h3>
                  <p className="mt-1 text-sm text-gray-500">Bệnh nhân chưa ghi nhận triệu chứng nào.</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {symptoms.map((symptom) => (
                <Card key={symptom.id}>
                  <CardHeader>
                    <CardTitle>{symptom.symptom_name}</CardTitle>
                    <CardDescription>Ghi nhận: {formatDate(symptom.recorded_date)}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div>
                        <h4 className="font-medium">Mức độ</h4>
                        <p className="text-sm text-gray-600">
                          {symptom.severity === "mild"
                            ? "Nhẹ"
                            : symptom.severity === "moderate"
                              ? "Trung bình"
                              : symptom.severity === "severe"
                                ? "Nặng"
                                : symptom.severity}
                        </p>
                      </div>
                      <div>
                        <h4 className="font-medium">Mô tả</h4>
                        <p className="text-sm text-gray-600">{symptom.description || "Không có mô tả"}</p>
                      </div>
                      {symptom.notes && (
                        <div>
                          <h4 className="font-medium">Ghi chú</h4>
                          <p className="text-sm text-gray-600">{symptom.notes}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="contact" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Thông tin liên hệ</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Email</h4>
                  <p className="text-sm text-gray-600">{patientData.users?.email || "Không có thông tin"}</p>
                </div>
                <div>
                  <h4 className="font-medium">Số điện thoại</h4>
                  <p className="text-sm text-gray-600">{patientData.users?.phone || "Không có thông tin"}</p>
                </div>
                <div>
                  <h4 className="font-medium">Liên hệ khẩn cấp</h4>
                  <p className="text-sm text-gray-600">{patientData.emergency_contact || "Không có thông tin"}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export interface PatientDocument {
  id: string
  patient_id: string
  title: string
  description?: string
  document_type: "medical_report" | "lab_result" | "prescription" | "imaging" | "other"
  file_url: string
  file_type: string
  upload_date: string
  document_date?: string
  is_verified: boolean
  verified_by?: string
  verification_date?: string
  tags?: string[]
  created_at: string
  updated_at: string
}

export interface MedicalHistory {
  id: string
  patient_id: string
  history_type: "diagnosis" | "surgery" | "treatment" | "medication" | "allergy" | "family_history" | "other"
  title: string
  description?: string
  start_date?: string
  end_date?: string
  is_current: boolean
  is_verified: boolean
  verified_by?: string
  verification_date?: string
  created_at: string
  updated_at: string,
  diagnosis?: string,
  status?: "active" | "destructive" | "outline"
  patients?: {
    full_name: string
    id: string
  }
  medical_staff?: {
    full_name: string
    specialty?: string
  }
}

export interface Prescription {
  id: string
  patient_id: string
  prescribed_by?: string
  prescription_date: string
  expiry_date?: string
  status: "active" | "completed" | "cancelled"
  notes?: string
  created_at: string
  updated_at: string
  medications?: PrescriptionMedication[]
}

export interface PrescriptionMedication {
  id: string
  prescription_id: string
  medication_name: string
  dosage: string
  frequency: string
  duration?: string
  instructions?: string
  start_date?: string
  end_date?: string
  created_at: string
  updated_at: string
}

export interface MedicationSchedule {
  id: string
  patient_id: string
  prescription_medication_id?: string
  medication_name: string
  scheduled_time: string
  is_taken: boolean
  taken_at?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface HealthMetric {
  id: string
  patient_id: string
  metric_type: "weight" | "blood_pressure" | "temperature" | "blood_sugar" | "heart_rate" | "other"
  value: string
  unit: string
  measured_at: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface Symptom {
  id: string
  patient_id: string
  symptom_name: string
  severity: number
  start_date: string
  end_date?: string
  is_active: boolean
  notes?: string
  created_at: string
  updated_at: string
}

"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { Database } from "@/types/supabase"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertCircle, Calendar, Clock } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import type { MedicalHistory } from "@/types/patient-records"

interface PatientMedicalHistoryProps {
  patientId: string
}

export function PatientMedicalHistory({ patientId }: PatientMedicalHistoryProps) {
  const [medicalHistory, setMedicalHistory] = useState<MedicalHistory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient<Database>()

  useEffect(() => {
    async function fetchMedicalHistory() {
      try {
        setLoading(true)

        const { data, error } = await supabase
          .from("medical_history")
          .select("*")
          .eq("patient_id", patientId)
          .order("created_at", { ascending: false })

        if (error) throw error

        setMedicalHistory(data || [])
      } catch (err: any) {
        console.error("Error fetching medical history:", err)
        setError(err.message || "Không thể tải lịch sử y tế")
      } finally {
        setLoading(false)
      }
    }

    if (patientId) {
      fetchMedicalHistory()
    }
  }, [patientId, supabase])

  // Group medical history by type
  const diagnoses = medicalHistory.filter((item) => item.record_type === "diagnosis")
  const surgeries = medicalHistory.filter((item) => item.record_type === "surgery")
  const treatments = medicalHistory.filter((item) => item.record_type === "treatment")
  const hospitalizations = medicalHistory.filter((item) => item.record_type === "hospitalization")
  const others = medicalHistory.filter(
    (item) => !["diagnosis", "surgery", "treatment", "hospitalization"].includes(item.record_type),
  )

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  const renderHistoryItems = (items: MedicalHistory[]) => {
    if (items.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-muted-foreground">Không có dữ liệu</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {items.map((item) => (
          <div key={item.id} className="p-4 border rounded-md">
            <div className="flex items-start justify-between">
              <h4 className="font-medium">{item.title}</h4>
              <Badge variant="outline">{item.record_type}</Badge>
            </div>

            <div className="flex items-center mt-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4 mr-1" />
              <span>{new Date(item.date).toLocaleDateString("vi-VN")}</span>
              {item.hospital && (
                <>
                  <span className="mx-2">•</span>
                  <span>{item.hospital}</span>
                </>
              )}
              {item.doctor && (
                <>
                  <span className="mx-2">•</span>
                  <span>Bác sĩ: {item.doctor}</span>
                </>
              )}
            </div>

            {item.description && <p className="mt-2">{item.description}</p>}

            {item.treatment && (
              <div className="mt-2">
                <p className="font-medium">Phương pháp điều trị:</p>
                <p>{item.treatment}</p>
              </div>
            )}

            {item.medications && item.medications.length > 0 && (
              <div className="mt-2">
                <p className="font-medium">Thuốc:</p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {item.medications.map((med, index) => (
                    <Badge key={index} variant="secondary">
                      {med}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {item.notes && (
              <div className="mt-2">
                <p className="font-medium">Ghi chú:</p>
                <p>{item.notes}</p>
              </div>
            )}
          </div>
        ))}
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lịch sử y tế</CardTitle>
        <CardDescription>Lịch sử y tế của bệnh nhân</CardDescription>
      </CardHeader>
      <CardContent>
        {medicalHistory.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 mx-auto text-muted-foreground" />
            <p className="mt-2 text-muted-foreground">Bệnh nhân chưa có lịch sử y tế nào</p>
          </div>
        ) : (
          <Tabs defaultValue="all">
            <TabsList className="mb-4">
              <TabsTrigger value="all">Tất cả</TabsTrigger>
              <TabsTrigger value="diagnoses">Chẩn đoán</TabsTrigger>
              <TabsTrigger value="surgeries">Phẫu thuật</TabsTrigger>
              <TabsTrigger value="treatments">Điều trị</TabsTrigger>
              <TabsTrigger value="hospitalizations">Nhập viện</TabsTrigger>
              {others.length > 0 && <TabsTrigger value="others">Khác</TabsTrigger>}
            </TabsList>

            <TabsContent value="all">{renderHistoryItems(medicalHistory)}</TabsContent>

            <TabsContent value="diagnoses">{renderHistoryItems(diagnoses)}</TabsContent>

            <TabsContent value="surgeries">{renderHistoryItems(surgeries)}</TabsContent>

            <TabsContent value="treatments">{renderHistoryItems(treatments)}</TabsContent>

            <TabsContent value="hospitalizations">{renderHistoryItems(hospitalizations)}</TabsContent>

            {others.length > 0 && <TabsContent value="others">{renderHistoryItems(others)}</TabsContent>}
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}

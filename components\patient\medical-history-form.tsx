"use client"

import type React from "react"

import { useState } from "react"
import { createClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { vi } from "date-fns/locale"
import { CalendarIcon, Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Checkbox } from "@/components/ui/checkbox"

interface MedicalHistoryFormProps {
  patientId: string
}

export function MedicalHistoryForm({ patientId }: MedicalHistoryFormProps) {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [historyType, setHistoryType] = useState<string>("")
  const [startDate, setStartDate] = useState<Date | undefined>(new Date())
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  const [isCurrent, setIsCurrent] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const supabase = createClient()
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!title || !historyType || !startDate) {
      toast({
        title: "Thiếu thông tin",
        description: "Vui lòng điền đầy đủ thông tin bắt buộc",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const { error } = await supabase.from("medical_history").insert({
        patient_id: patientId,
        title,
        description,
        history_type: historyType,
        start_date: startDate?.toISOString(),
        end_date: !isCurrent ? endDate?.toISOString() : null,
        is_current: isCurrent,
      })

      if (error) throw error

      toast({
        title: "Thêm thành công",
        description: "Thông tin tiền sử bệnh đã được thêm thành công",
      })
      console.log("Medical history added successfully")

      // Reset form
      setTitle("")
      setDescription("")
      setHistoryType("")
      setStartDate(new Date())
      setEndDate(undefined)
      setIsCurrent(false)
      window.location.reload() 
    } catch (error) {
      console.error("Error adding medical history:", error)
      toast({
        title: "Lỗi",
        description: "Đã xảy ra lỗi khi thêm thông tin. Vui lòng thử lại.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="title">
            Tiêu đề <span className="text-red-500">*</span>
          </Label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Ví dụ: Phẫu thuật ruột thừa"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="history-type">
            Loại <span className="text-red-500">*</span>
          </Label>
          <Select value={historyType} onValueChange={setHistoryType} required>
            <SelectTrigger id="history-type">
              <SelectValue placeholder="Chọn loại" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="diagnosis">Chẩn đoán</SelectItem>
              <SelectItem value="surgery">Phẫu thuật</SelectItem>
              <SelectItem value="treatment">Điều trị</SelectItem>
              <SelectItem value="medication">Thuốc</SelectItem>
              <SelectItem value="allergy">Dị ứng</SelectItem>
              <SelectItem value="family_history">Tiền sử gia đình</SelectItem>
              <SelectItem value="other">Khác</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Mô tả chi tiết</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Mô tả chi tiết về tiền sử bệnh"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="start-date">
            Ngày bắt đầu <span className="text-red-500">*</span>
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button id="start-date" variant="outline" className="w-full justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "PPP", { locale: vi }) : <span>Chọn ngày</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label htmlFor="end-date">Ngày kết thúc</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="end-date"
                variant="outline"
                className="w-full justify-start text-left font-normal"
                disabled={isCurrent}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "PPP", { locale: vi }) : <span>Chọn ngày</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
                disabled={(date) => (startDate ? date < startDate : false)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="is-current"
          checked={isCurrent}
          onCheckedChange={(checked) => {
            setIsCurrent(checked === true)
            if (checked) {
              setEndDate(undefined)
            }
          }}
        />
        <Label htmlFor="is-current">Đang tiếp diễn</Label>
      </div>

      <Button type="submit" className="w-full" disabled={isSubmitting}>
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Đang xử lý...
          </>
        ) : (
          "Thêm tiền sử bệnh"
        )}
      </Button>
    </form>
  )
}

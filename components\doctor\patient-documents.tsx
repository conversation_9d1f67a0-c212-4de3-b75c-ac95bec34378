"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { Database } from "@/types/supabase"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertCircle, Download, Eye, FileText, Search } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import type { PatientDocument } from "@/types/patient-records"

interface PatientDocumentsProps {
  patientId: string
}

export function PatientDocuments({ patientId }: PatientDocumentsProps) {
  const [documents, setDocuments] = useState<PatientDocument[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDocument, setSelectedDocument] = useState<PatientDocument | null>(null)
  const [viewerOpen, setViewerOpen] = useState(false)
  const supabase = createClientComponentClient<Database>()

  useEffect(() => {
    async function fetchDocuments() {
      try {
        setLoading(true)

        const { data, error } = await supabase
          .from("patient_documents")
          .select("*")
          .eq("patient_id", patientId)
          .order("created_at", { ascending: false })

        if (error) throw error

        setDocuments(data || [])
      } catch (err: any) {
        console.error("Error fetching patient documents:", err)
        setError(err.message || "Không thể tải tài liệu bệnh nhân")
      } finally {
        setLoading(false)
      }
    }

    if (patientId) {
      fetchDocuments()
    }
  }, [patientId, supabase])

  const filteredDocuments = documents.filter(
    (doc) =>
      doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.document_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doc.description && doc.description.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  const handleViewDocument = (document: PatientDocument) => {
    setSelectedDocument(document)
    setViewerOpen(true)
  }

  const handleDownload = async (document: PatientDocument) => {
    try {
      const { data, error } = await supabase.storage.from("patient_documents").download(document.file_path)

      if (error) throw error

      // Create a download link
      const url = URL.createObjectURL(data)
      const a = document.createElement("a")
      a.href = url
      a.download = document.title
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error("Error downloading document:", err)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tài liệu y tế</CardTitle>
        <CardDescription>Xem tài liệu y tế của bệnh nhân</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="relative mb-4">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm tài liệu..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {filteredDocuments.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground" />
            <p className="mt-2 text-muted-foreground">
              {documents.length === 0 ? "Bệnh nhân chưa tải lên tài liệu nào" : "Không tìm thấy tài liệu phù hợp"}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredDocuments.map((doc) => (
              <div key={doc.id} className="flex items-start p-3 border rounded-md">
                <div className="flex-shrink-0 mr-3">
                  <FileText className="h-10 w-10 text-blue-500" />
                </div>
                <div className="flex-grow min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium truncate">{doc.title}</h4>
                    <Badge variant="outline">{doc.document_type}</Badge>
                  </div>
                  {doc.description && (
                    <p className="text-sm text-muted-foreground line-clamp-2 mt-1">{doc.description}</p>
                  )}
                  <div className="flex items-center justify-between mt-2">
                    <p className="text-xs text-muted-foreground">
                      Tải lên: {new Date(doc.created_at).toLocaleDateString("vi-VN")}
                    </p>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleViewDocument(doc)}>
                        <Eye className="h-4 w-4 mr-1" />
                        Xem
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDownload(doc)}>
                        <Download className="h-4 w-4 mr-1" />
                        Tải xuống
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        <Dialog open={viewerOpen} onOpenChange={setViewerOpen}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>{selectedDocument?.title}</DialogTitle>
            </DialogHeader>
            <div className="mt-4">
              {selectedDocument && (
                <iframe
                  src={`${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/patient_documents/${selectedDocument.file_path}`}
                  className="w-full h-[70vh] border rounded"
                />
              )}
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}
